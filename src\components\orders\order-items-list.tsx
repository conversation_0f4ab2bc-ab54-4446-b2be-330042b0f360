/**
 * @file order-items-list.tsx
 * @description Component to display the list of order items in an empty table.
 */

import React from 'react';
import { InventoryItem } from '@/database/schema';
import { generateProductDescription } from '@/lib/utils/productDescription';

/**
 * @typedef {Object} OrderItem
 * @property {InventoryItem} item - The inventory item.
 * @property {number} quantity - The quantity of the item.
 * @property {number} price - The price per unit of the item.
 * @property {number} subtotal - The subtotal price for the item (quantity * price).
 */
export type OrderItem = {
  item: InventoryItem;
  quantity: number;
  price: number;
  subtotal: number;
};

interface OrderItemsListProps {
  retailerId: string | null;
  retailerName: string;
  retailerLocation: string;
  orderItems: OrderItem[];
  onQuantityChange: (index: number, quantity: number) => void;
  onClearOrder: () => void;
  orderCode: string; // Add orderCode prop
  className?: string;
  onSubmitOrder: () => void;
}

/**
 * Renders an empty table for displaying order items.
 *
 * @returns {JSX.Element} The order items list table component.
 */
export function OrderItemsList({ orderItems, onQuantityChange, onClearOrder, className, orderCode, onSubmitOrder }: OrderItemsListProps): React.ReactElement {
  return (
    <div className={`w-full p-4 border rounded-md ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Order Items</h3>
        {orderCode && <span className="ml-4 text-sm text-gray-600">Order Code: {orderCode}</span>}
        {orderItems.length > 0 && (
          <button
            onClick={onClearOrder}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
          >
            Clear Order List
          </button>
        )}
      </div>
      <div className="overflow-x-auto">
        <div className="max-h-60 overflow-y-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">S.No</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Product Description</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Quantity Left</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Quantity</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Price</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Subtotal</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orderItems.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                    No order items added yet.
                  </td>
                </tr>
              ) : (
                orderItems.map((orderItem, index) => (
                  <tr key={orderItem.item.product_id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{index + 1}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {generateProductDescription(orderItem.item)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{orderItem.item.quantity_left ?? 0}</td>
                    
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <input
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                      value={orderItem.quantity}
                        onChange={(e) => onQuantityChange(index, parseInt(e.target.value) || 0)}
                        className="w-16 px-2 py-1 text-center border-b border-gray-300 focus:outline-none focus:border-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{orderItem.price.toFixed(2)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{orderItem.subtotal.toFixed(2)}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      {orderItems.length > 0 && (
        <div className="mt-4 border-t pt-4 flex justify-end items-center space-x-5">
          <span className="text-xl font-bold text-gray-500 border-b border-gray-300 pb-1">
            Total: {orderItems.reduce((sum, item) => sum + item.subtotal, 0).toFixed(2)}
          </span>
          <button 
            type="button"
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            onClick={onSubmitOrder}>
              Create
          </button>
        </div>
      )}
      </div>
      </div>
  );
}