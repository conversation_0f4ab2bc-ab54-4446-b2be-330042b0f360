"use client";

import * as React from "react";
import { Input } from "@/components/ui/input";
import { RetailerCombobox } from "@/components/ui/RetailerCombobox";
import { Label } from "@/components/ui/label";
import { OrderItem } from "./order-items-list";
import { InventoryItem } from "@/database/schema";
import { OrderItemSearch } from "./order-item-search";
import { OrderSearchResults } from "./order-search-results";
import { OrderItemsList } from "./order-items-list";
import { generateProductDescription } from "@/lib/utils/productDescription";
import { createOrder, createOrderBillItems, NewOrder, NewOrderBillItem } from "@/lib/api/orders";
import { useAuthStore } from "@/lib/store/authStore";
import { useInventoryRealtimeSync } from "@/lib/realtime/useInventoryRealtimeSync";
import { useOrdersStore } from "@/lib/store/ordersStore";
/**
 * @typedef {Object} CreateOrderFormProps

 * @property {string} orderCode - The auto-generated order code.
 * @property {string} issuedBy - The full name of the currently logged-in user.
 */

/**
 * Renders the form for creating a new order.
 * @param {CreateOrderFormProps} props - The component props.
 * @returns {JSX.Element} The rendered form.
 */
export function CreateOrderForm({ orderCode, issuedBy, onOrderCreated }: { orderCode: string; issuedBy: string; onOrderCreated?: () => void }) {
  // Subscribe to real-time inventory updates (without toast notifications in order context)
  useInventoryRealtimeSync();

  const { user, fullName } = useAuthStore() as { user: { id: string } | null; fullName: string };
  const { addOrder } = useOrdersStore();
  const [orderItems, setOrderItems] = React.useState<OrderItem[]>([]);
  const [selectedRetailerId, setSelectedRetailerId] = React.useState<string | null>(null);
  const [selectedRetailerDisplayName, setSelectedRetailerDisplayName] = React.useState<string | null>(null);
  const [location, setLocation] = React.useState("");
  const [searchResults, setSearchResults] = React.useState<{
    items: InventoryItem[];
    totalCount: number;
  }>({ items: [], totalCount: 0 });

  const handleSearchResultsChange = (results: { items: InventoryItem[]; totalCount: number }) => {
    setSearchResults(results);
  };

  const handleAddToOrderItems = (item: InventoryItem) => {
    console.log('handleAddToOrderItems called with item:', item);

    setOrderItems((prevItems) => {
      const existingItemIndex = prevItems.findIndex((orderItem) => orderItem.item.product_id === item.product_id);

      if (existingItemIndex > -1) {
        const updatedItems = [...prevItems];
        const existingItem = updatedItems[existingItemIndex];
        updatedItems[existingItemIndex] = {
          ...existingItem,
          quantity: existingItem.quantity + 1,
          subtotal: (existingItem.quantity + 1) * existingItem.price,
        };
        return updatedItems;
      } else {
        const price = item.price_per_unit || 0; // Assuming price_per_unit exists on InventoryItem
        return [
          ...prevItems,
          {
            item,
            quantity: 1,
            price,
            subtotal: price,
          },
        ];
      }
    });
  };

  const handleQuantityChange = (index: number, quantity: number) => {
    setOrderItems((prevItems) => {
      const updatedItems = [...prevItems];
      const itemToUpdate = updatedItems[index];
      if (itemToUpdate) {
        updatedItems[index] = {
          ...itemToUpdate,
          quantity: quantity,
          subtotal: quantity * itemToUpdate.price,
        };
      }
      return updatedItems;
    });
  };

  const handleClearOrder = () => {
    setOrderItems([]);
  };

  const handleOrderCreation = async () => {
    if (!selectedRetailerId || selectedRetailerId.trim() === "") {
      alert("Please select a retailer before confirming the order.");
      return;
    }
    if (!location || location.trim() === "") {
      alert("Please enter a location before confirming the order.");
      return;
    }

    console.log('handleOrderCreation (API call) initiated.');

    const newOrderData: NewOrder = {
      order_code: orderCode,
      issued_by_display_name: fullName || issuedBy, // Use fullName from auth store, fallback to prop
      order_issued_by: user?.id || 'unknown_id',
      order_location: location,
      order_status: 'pending', // Default status
      retailer_display_name: selectedRetailerDisplayName || '',
      retailer_id: selectedRetailerId || '',
    };

    try {
      const createdOrder = await createOrder(newOrderData);

      if (createdOrder) {
        const orderId = createdOrder.order_id;

        const newOrderBillItems: NewOrderBillItem[] = orderItems.map(item => ({
          product_id: item.item.product_id,
          product_description: generateProductDescription(item.item),
          quantity: item.quantity,
          price_per_unit: item.price,
          order_id: orderId,
        }));

        const createdBillItems = await createOrderBillItems(orderId, newOrderBillItems);

        if (createdBillItems) {
          // Order remains 'pending' after creation
          console.log('Order and bill items created successfully:', createdOrder, createdBillItems);
          console.log('Order creation process completed.');

          // Add the new order to the orders store
          addOrder(createdOrder);

          // Optionally, clear the form or provide user feedback
          handleClearOrder();
          setSelectedRetailerId(null);
          setSelectedRetailerDisplayName(null);
          setLocation('');
          if (onOrderCreated) onOrderCreated();
          alert('Order created successfully!');
        } else {
          console.error('Failed to create order bill items.');
      console.log('Order creation process failed at bill items.');

          alert('Failed to create order bill items.');
        }
      } else {
        console.error('Failed to create order.');
      console.log('Order creation process failed at order creation.');

        alert('Failed to create order.');
      }
    } catch (error) {
      console.error('Error submitting order:', error);
      console.log('Order creation process encountered an error.');

      alert('Error submitting order.');
    }
  };

  return (
    <form onSubmit={e => e.preventDefault()}>
      <div className="grid gap-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="retailer" className="text-right">
              Retailer
            </Label>
            <div className="col-span-2">
              <RetailerCombobox
                selectedRetailerId={selectedRetailerId}
                onSelectRetailer={(id, name) => {
                  setSelectedRetailerId(id);
                  setSelectedRetailerDisplayName(name);
                }}
              />
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="location" className="text-right">
              Location
            </Label>
            <Input id="location" placeholder="Enter location" className="col-span-2" value={location} onChange={(e) => setLocation(e.target.value)} />
          </div>
        </div>
        <OrderItemSearch onSearchResultsChange={handleSearchResultsChange} />
        <div className="flex flex-col md:flex-row gap-4">
          <OrderSearchResults searchResults={searchResults} onAddToOrder={handleAddToOrderItems} className="md:w-2/5" />
           <OrderItemsList
             orderItems={orderItems}
             onQuantityChange={handleQuantityChange}
             onClearOrder={handleClearOrder}
             retailerId={selectedRetailerId}
             retailerName={selectedRetailerDisplayName || ''}
             retailerLocation={location}
             orderCode={orderCode}
             className="md:w-3/5"
             onSubmitOrder={handleOrderCreation}
           />
        </div>
      </div>
    </form>
  );
}