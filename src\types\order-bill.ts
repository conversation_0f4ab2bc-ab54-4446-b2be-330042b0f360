/**
 * @module types/order-bill
 * @description Defines the TypeScript types for order bill-related data.
 */

/**
 * @typedef {Object} OrderBill
 * @property {string} id - Unique identifier for the order bill.
 * @property {string} order_id - The ID of the associated order.
 * @property {number} total_amount - The total amount of the bill.
 * @property {Date} created_at - The timestamp when the order bill was created.
 */
export type OrderBill = {
  bill_id: string;
  order_id: string;
  created_at: Date | null;
  total_items: number | null;
  total_amount: number | null;
  payment_status: string | null;
  notes: string | null;
};