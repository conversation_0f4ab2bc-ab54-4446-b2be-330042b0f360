import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Order } from '@/types/order';

interface OrderStore {
  orders: Order[];
  updateOrder: (order: Order) => void;
  setOrders: (orders: Order[]) => void;
}

export const useOrderStore = create<OrderStore>()(
  persist(
    (set, get) => ({
      orders: [],
      updateOrder: (order) => {
        set((state) => {
          const idx = state.orders.findIndex((o) => o.order_id === order.order_id);
          if (idx !== -1) {
            // Update existing order
            const updated = [...state.orders];
            updated[idx] = order;
            return { orders: updated };
          } else {
            // Add new order
            return { orders: [order, ...state.orders] };
          }
        });
      },
      setOrders: (orders) => set({ orders }),
    }),
    { name: 'order-store' }
  )
);
