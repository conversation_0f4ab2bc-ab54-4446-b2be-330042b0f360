import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, CheckCircle } from "lucide-react";
import { InventoryItem } from "@/database/schema";

interface SelectionActionCellProps {
  inventoryItem: InventoryItem;
  selectedProducts: InventoryItem[];
  setSelectedProducts: (items: InventoryItem[]) => void;
}

export const SelectionActionCell: React.FC<SelectionActionCellProps> = ({
  inventoryItem,
  selectedProducts,
  setSelectedProducts,
}) => {
  const isSelected = selectedProducts.some(
    (p) => p.product_id === inventoryItem.product_id
  );

  const toggleSelection = () => {
    if (isSelected) {
      setSelectedProducts(
        selectedProducts.filter((p) => p.product_id !== inventoryItem.product_id)
      );
    } else {
      setSelectedProducts([...selectedProducts, inventoryItem]);
    }
  };

  return (
    <Button variant="ghost" size="icon" onClick={toggleSelection}>
      {isSelected ? (
        <CheckCircle className="h-5 w-5 text-green-500" />
      ) : (
        <PlusCircle className="h-5 w-5 text-gray-500" />
      )}
    </Button>
  );
};
