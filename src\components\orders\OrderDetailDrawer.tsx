import * as React from "react";
import { Drawer, DrawerContent, DrawerClose, DrawerTitle } from "../ui/drawer";
import { Button } from "@/components/ui/button";
import { Order } from "@/types/order";
import { OrderBillItem } from "@/types/order-bill-item";

// Glossy dark background for the whole drawer
const drawerBg = "bg-gradient-to-br from-[#232b3a] via-[#232b3a] to-[#232b3a]";

interface OrderDetailDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: Order | null;
  billItems: OrderBillItem[];
}

export const OrderDetailDrawer: React.FC<OrderDetailDrawerProps> = ({ open, onOpenChange, order, billItems }) => {
  if (!order) return null;
  const grandTotal = billItems.reduce((sum, item) => sum + (item.subtotal || 0), 0);

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className={drawerBg + " text-white"} aria-describedby={undefined}>
        <DrawerTitle>Order Details</DrawerTitle>
        <div className="w-full px-0 md:px-12 pt-2 pb-8">
          {/* Close Button Only */}
          <div className="flex justify-end px-0 md:px-2 pb-2">
            <DrawerClose asChild>
              <Button variant="ghost" size="icon" className="text-lg font-bold text-white hover:text-gray-200">
                ×
              </Button>
            </DrawerClose>
          </div>
          {/* Side by side content */}
          <div className="flex flex-col md:flex-row gap-8 px-0 md:px-2 py-2">
            {/* Order Info Left */}
            <div className="flex-1 min-w-[250px] pr-8 border-r border-gray-500">
              <div className="mb-2"><span className="font-bold">Order Code:</span> <span className="font-semibold">{order.order_code}</span></div>
              <div className="mb-3"><span className="font-bold">Retailer:</span> {order.retailer_display_name}</div>
              <div className="mb-3"><span className="font-bold">Issued By:</span> {order.issued_by_display_name}</div>
              <div className="mb-3"><span className="font-bold">Order Location:</span> {order.order_location}</div>
              <div className="mb-3"><span className="font-bold">Created At:</span> {order.created_at ? order.created_at.toString() : "-"}</div>
              <div className="mb-3"><span className="font-bold">Status:</span> {order.order_status}</div>
            </div>
            {/* Bill Items Right */}
            <div className="flex-1 pl-0 md:pl-8">
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm border rounded-lg overflow-hidden">
                  <thead>
                    <tr>
                      <th className="border px-2 py-1">S.No</th>
                      <th className="border px-2 py-1">Product Description</th>
                      <th className="border px-2 py-1">Quantity</th>
                      <th className="border px-2 py-1">Unit Price</th>
                      <th className="border px-2 py-1">Subtotal</th>
                    </tr>
                  </thead>
                  <tbody>
                    {billItems.map((item, idx) => (
                      <tr key={item.item_id}>
                        <td className="border px-2 py-1 text-center">{idx + 1}</td>
                        <td className="border px-2 py-1">{item.product_description}</td>
                        <td className="border px-2 py-1 text-center">{item.quantity}</td>
                        <td className="border px-2 py-1 text-right">{item.price_per_unit}</td>
                        <td className="border px-2 py-1 text-right">{item.subtotal}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colSpan={4} className="border px-2 py-2 text-right font-bold text-lg">Grand Total</td>
                      <td className="border px-2 py-2 text-right font-bold text-lg">{grandTotal}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};
