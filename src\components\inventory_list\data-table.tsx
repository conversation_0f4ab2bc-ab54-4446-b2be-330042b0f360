"use client";

import * as React from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  VisibilityState,
} from "@tanstack/react-table";
import { useInventoryStore } from '@/lib/store/inventoryStore';
import { InventoryItem } from '@/database/schema';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";



import { Button } from "@/components/ui/button";
import { SelectedProductsDialog } from "./SelectedProductsDialog";
import { getInventoryColumns } from './columns';

/**
 * Props for the DataTable component.
 * @template TData The type of the data in the table.
 * @template TValue The type of the value in a column.
 * @property {ColumnDef<TData, TValue>[]} columns - An array of column definitions.
 * @property {TData[]} data - The data to be displayed in the table.
 */
interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  totalItemCount: number;
}

/**
 * A generic data table component built with TanStack Table and shadcn/ui.
 * @template TData The type of the data in the table.
 * @template TValue The type of the value in a column.
 * @param {DataTableProps<TData, TValue>} props - The props for the component.
 * @returns {JSX.Element} The rendered data table.
 */
export function DataTable<TData, TValue>({
  columns,
  data,
  totalItemCount,
}: DataTableProps<TData, TValue>): React.ReactElement {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>(
    {}
  );
  const { showLensParameters, toggleLensParameters, selectedProducts, setSelectedProducts } = useInventoryStore();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    getRowId: (row) => (row as InventoryItem).product_id, // Use product_id as the unique row ID
    enableRowSelection: true, // Enable row selection for the table
    state: {
      sorting,
      columnVisibility: {
        ...columnVisibility,
        spherical_value: showLensParameters,
        cylindrical_value: showLensParameters,
        axis_value: showLensParameters,
        add_value: showLensParameters,
        lens_side: showLensParameters,
      },
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center py-2 justify-between">
        <div className="text-sm text-foreground">
          {selectedProducts.length > 0 && (
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(true)}
            >
              View Selected Items ({selectedProducts.length})
            </Button>
          )}
        </div>
        <div className="flex items-center ml-auto">
          <span className="text-sm text-muted-foreground mr-4">
            {totalItemCount} items
          </span>
          <Button
            variant="outline"
            onClick={toggleLensParameters}
          >
            {showLensParameters ? "Hide Lens Params" : "Show Lens Params"}
          </Button>
          <Button variant="outline" className="mr-2">
            Export All Items
          </Button>
          
        </div>
        <SelectedProductsDialog
          isOpen={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          products={selectedProducts}
          onClearSelection={() => setSelectedProducts([])}
        />
      </div>
      <div className="rounded-lg shadow-lg overflow-hidden border-2 border-gray-700">
        <Table>
          <TableHeader className="bg-gray-800 [&_tr]:hover:bg-gray-800">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-white font-bold border-r-2 border-gray-700 last:border-r-0">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className="border-b-2 border-gray-200 last:border-b-0" // Add bottom border to rows
                 >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="border-r-2 border-gray-200 last:border-r-0">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

/**
 * InventoryDataTable component specifically for inventory items.
 * @param {Object} props - The component props.
 * @param {InventoryItem[]} props.data - The inventory data.
 * @param {number} props.totalItemCount - The total number of inventory items.
 * @returns {JSX.Element} The rendered inventory data table.
 */
export function InventoryDataTable({ data, totalItemCount }: { data: InventoryItem[]; totalItemCount: number }) {
  const { selectedProducts, setSelectedProducts } = useInventoryStore();
  const columns = React.useMemo(() => getInventoryColumns(selectedProducts, setSelectedProducts), [selectedProducts, setSelectedProducts]);
  return <DataTable columns={columns} data={data} totalItemCount={totalItemCount} />;
}
