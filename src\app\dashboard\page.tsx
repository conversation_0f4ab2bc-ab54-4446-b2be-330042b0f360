'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/store/authStore';

export default function DashboardPage() {
  const router = useRouter();
  const { role } = useAuthStore();

  useEffect(() => {
    if (role === 'admin') {
      router.push('/dashboard/admin');
    } else if (role === 'reception') {
      router.push('/dashboard/reception');
    } else if (role === 'finance') {
      router.push('/dashboard/finance');
    } else {
      // Handle unauthorized access or redirect to login
      router.push('/login');
    }
  }, [role, router]);

  return (
    <div className="flex items-center justify-center h-full">
      <p>Redirecting based on your role...</p>
    </div>
  );
}