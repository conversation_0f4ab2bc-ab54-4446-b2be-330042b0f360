/**
 * @module @/database/schema
 * @description Defines TypeScript types for Supabase tables: `products` and `lensparameters`.
 */

/**
 * @typedef {Object} Product
 * @property {string} product_id - The unique identifier for the product (UUID).
 * @property {number} product_code - The product code (numeric).
 * @property {number | null} quantity_initial - Initial quantity of the product.
 * @property {number | null} quantity_added - Quantity added to the product.
 * @property {number | null} quantity_total - Total quantity of the product.
 * @property {number | null} quantity_out - Quantity of the product sold or removed.
 * @property {number | null} quantity_left - Remaining quantity of the product.
 * @property {number | null} restock_threshold - Threshold for restocking the product.
 * @property {string} entry_date - The date and time of product entry (ISO 8601 string).
 * @property {string | null} product_category - The category of the product.
 * @property {string} power_type - The power type of the product, defaults to 'Plano'.
 */
export type Product = {
  product_id: string;
  product_code: number;
  quantity_initial: number;
  quantity_added: number | null;
  quantity_total: number | null;
  quantity_out: number | null;
  quantity_left: number | null;
  restock_threshold: number | null;
  entry_date: string;
  product_category: string | null;
  power_type: string;
  price_per_unit: number | null;
  spherical_value: number | null;
  cylindrical_value: number | null;
  axis_value: number | null;
  add_value: number | null;
  lens_side: 'Left' | 'Right' | null;
  updated_at: string | null;
  quantity_reserved: number | null;
};

export type InventoryItem = Product;

export type StaffMember = {
  user_id: string;
  full_name: string | null;
  staff_role: string | null;
  phone_number: string | null;
  address: string | null;
};

export type Retailer = {
  retailer_id: string;
  retailer_name: string;
  retailer_location: string | null;
  retailer_number: string | null;
};