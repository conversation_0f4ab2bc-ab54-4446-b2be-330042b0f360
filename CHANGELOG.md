# Changelog

## [Unreleased]

### Added
- Refactored `CreateProductDialog.tsx` to use a centralized API function (`createProduct` in `src/lib/api/products.ts`) for product creation, improving separation of concerns and maintainability.
- Implemented IndexedDB persistence for newly added product data by integrating with the Zustand `useInventoryStore`, ensuring offline availability and data consistency.
- Extracted `productCategories`, `powerTypes`, and `lensSides` into `src/lib/data/product_constants.ts` for reusability.
- Added "Clear Filters" button to `AdvancedFilter.tsx` and implemented `clearFilters` action in `inventoryStore.ts`.
- Modified `src/components/inventory_list/columns.tsx` and `src/components/inventory_list/SelectedProductsDialog.tsx` to display `quantity_left` instead of `quantity_total`.
- Modified `src/lib/api/products.ts` to update `quantity_left` and `quantity_added` in `bulkUpdateProductQuantity`, while `quantity_total` remains a historical record.
- Refactored `generateProductDescription` function from `src/components/inventory_list/SelectedProductsDialog.tsx` into a reusable utility function in `src/lib/utils/productDescription.ts`.
- Added `bulkReductProductQuantity` function to `src/lib/api/products.ts` to reduce `quantity_left` and increase `quantity_out` for selected products.
- Defined and updated TypeScript types for `Order`, `OrderBill`, and `OrderBillItem` in `src/types/order.ts`, `src/types/order-bill.ts`, and `src/types/order-bill-item.ts` respectively, to precisely match the provided SQL schemas.
```
- Refactored `create-order-form.tsx` to directly receive `orderCode` and `issuedBy` as props from `create-order-dialog.tsx`, removing unnecessary setter props.
- Removed `console.log` statements from `create-order-form.tsx` related to order item additions, ensuring logging only occurs on final order confirmation.
- **Feature**: Implemented dynamic order code generation (`ORD-YYYYMMDD-HHMMSS[A/P]`) to ensure a unique code is generated each time the 'Create New Order' dialog is opened.
- **Feature**: Implemented full order creation flow, including:
  - Adding `NewOrder` and `NewOrderBillItem` types in `src/lib/api/orders.ts`.
  - Modifying `createOrder` to accept `NewOrder` type.
  - Adding `createOrderBillItems` function to handle bulk insertion of order bill items.
  - Integrating order and order bill item creation into `create-order-form.tsx`'s `handleSubmit` function, ensuring data is sent to Supabase and providing user feedback.
- Added `createOrder` function in `src/lib/api/orders.ts` to handle new order submission.
- Integrated `createOrder` into `create-order-form.tsx`'s `handleSubmit` for order persistence.
- Modified `order-items-list.tsx` to display the order code and removed its internal `console.log`.
- Passed `orderCode` prop from `create-order-form.tsx` to `OrderItemsList`.
```
- Fixed import path for `useAuthStore` in `create-order-form.tsx`.

- Added a confirmation dialog for the delete action in `src/components/inventory_list/SelectedProductsDialog.tsx` to prevent accidental deletions.
- Styled the "Confirm Delete" button in `SelectedProductsDialog.tsx` red using `variant="destructive".
- Repositioned the "Clear Selected List" button in `SelectedProductsDialog.tsx` to be below the "Export" and "Delete" buttons, and adjusted its width to span their combined width.
- Refactored `AlertDialog` components from `SelectedProductsDialog.tsx` into a new reusable component file: `src/components/ui/alert-dialog.tsx`.
- Reorganized UI of bulk update/reduce fields and buttons, and moved 'Clear Selected Items' button in `src/components/inventory_list/SelectedProductsDialog.tsx`.
- Added the count of selected products to the "View Selected Items" button in `src/components/inventory_list/data-table.tsx`.
- Updated `data-table.tsx` to display the total item count using the `totalItemCount` prop instead of the filtered row count.
- Passed `totalCount` prop to `DataTable` component in `src/app/dashboard/reception/inventory/page.tsx` to ensure correct total item count display.
- Added bulk reduce quantity input and button in `src/components/inventory_list/SelectedProductsDialog.tsx`.
- Implemented product code filter in `AdvancedFilter.tsx`.
- Further enhanced `src/components/orders/order-item-search.tsx` to display detailed product information in the console when the simulated search returns 5 or fewer items.
- Integrated actual inventory data filtering from `useInventoryStore` in `src/components/orders/order-item-search.tsx`, replacing simulated results with real-time filtering based on product category, SPH, CYL, ADD, and AXIS values.
- Enhanced `order-item-search.tsx`:
  - Added `product_code` and `lens_side` as new search parameters.
  - Updated `handleSearch` to filter by these new parameters.
  - Refactored search results display from a list to a table with columns for product code, description, quantity left, and an "Add to List" action button.
  - Implemented logic to limit search results display to a maximum of 5 items, with a message indicating more results if applicable.
  - Added appropriate messages for empty search results based on whether a search was performed.
  - Fixed parsing error in conditional rendering logic.
- Increased dialog width for `CreateOrderDialog` in `create-order-dialog.tsx` to `sm:max-w-[1200px]` for better product search and order item display.
- Fixed parsing error in `order-item-search.tsx` due to an extra closing `</div>` tag.
- Removed `lens_side` search parameter from `order-item-search.tsx`.
- Implemented debounced search for `product_code` in `order-item-search.tsx` with a minimum of 5 characters.
- Updated the search results message in `order-item-search.tsx` to display the total number of results when truncated.
- Reordered search filters in `order-item-search.tsx` to prioritize product code, and adjusted the width of the search results list.
- Removed debounced search for SPH, CYL, ADD, and AXIS fields in `order-item-search.tsx`, ensuring searches for these fields are triggered only by the search button.
- Removed the product code length error message from `order-item-search.tsx`.
- Implemented a `hasSearched` state in `order-item-search.tsx` to control the visibility of the "No products found" message, ensuring it only appears after a search is performed.
- Adjusted the width of the product code input field and other search filter input fields (SPH, CYL, ADD, AXIS) in `order-item-search.tsx` to optimize layout and fit elements on the same row.
- Removed `className` prop from `Combobox` component in `order-item-search.tsx` to resolve a type error.
- **Feature**: Enhanced order item management in `CreateOrderForm` and `OrderItemsList`.
  - `OrderItemsList` now dynamically displays added items with S.No, product description (generated by `generateProductDescription`), editable quantity, price per unit, and auto-calculated total.
  - Added a subtotal display and a "Clear Order List" button to `OrderItemsList`.
  - `CreateOrderForm` now manages the state for order items, including adding items from search results, updating quantities, and clearing the list. The `item_description` in the final order data is now correctly generated using `generateProductDescription`.
  - `OrderSearchResults` now includes an "Add to List" button for each item, which adds the item to the order list in `CreateOrderForm`.
- **Fix**: Added `price_per_unit` to the `Product` type in `src/database/schema.ts` to resolve a TypeScript error and correctly reflect product pricing.
- **UI Enhancement**: Optimized `OrderSearchResults` table layout by replacing the "Action" column with a compact plus icon and adjusting column widths for better fit.
- **Feature**: Implemented product code validation in `OrderItemSearch` to require a minimum of 5 characters for search and display an error message if the condition is not met.
- **Enhancement**: Modified `OrderItemSearch` to ensure product code search is explicitly triggered by the "Search" button, rather than automatically via debounce.
- **Enhancement**: Adjusted the layout of `CreateOrderForm` to allocate 40% width to search results and 60% to order items for improved visual balance.
- **Enhancement**: Added `className` prop to `OrderItemsList` and `OrderSearchResults` components, enabling more flexible styling and layout adjustments.
- **Enhancement**: Implemented scrollable order items list in `OrderItemsList` to prevent dialog box height from increasing, ensuring a fixed height and consistent column widths, with vertical scrolling only.
- **UI Enhancement**:- Increased the width of the order creation dialog in `src/components/orders/create-order-dialog.tsx` to `1500px`.
- Removed the vertical padding (`py-4`) from the main content `div` in `src/components/orders/create-order-form.tsx` to eliminate the visual line separator.
- Modified `src/components/orders/order-item-search.tsx` to return a product list and item count based on search results, passing them via an `onSearchResultsChange` prop.
- Updated `src/components/orders/order-search-results.tsx` to accept `searchResults` as a prop, display product details, and show a red highlighted message if more than 5 results are found.
- **Fix**: Resolved TypeScript errors in `src/components/orders/order-search-results.tsx` by defining `OrderSearchResultsProps` as an interface and explicitly typing `item` in map function.
- **Fix**: Resolved TypeScript errors in `src/components/orders/order-item-search.tsx` by defining `OrderItemSearchProps` as an interface and correctly typing `setSearchResults` calls.
- **Feature**: Modified "Add to List" button in `src/components/orders/order-search-results.tsx` to log product data to console.

### Changed
- Adjusted width of numerical input fields (SPH, CYL, ADD, AXIS) in `AdvancedFilter.tsx` for better visual alignment.
- **2024-07-29**: Implemented step intervals for SPH, CYL, and ADD fields (0.25) and AXIS field (90) in `src/components/inventory_list/AdvancedFilter.tsx`. Re-added AXIS input, product code input, lens side select, and the "Apply Filters" button to `src/components/inventory_list/AdvancedFilter.tsx`. Implemented logic to treat 0 as null for SPH, CYL, ADD, and AXIS input fields in `src/components/inventory_list/AdvancedFilter.tsx`.
- Fixed structural issues in `AdvancedFilter.tsx`, including the removal of duplicate `product_code` and `lens side` selection fields and the `apply filters` button, and corrected `div` nesting.
- Updated `useInventoryStore` to include `selectedProductCode` state and filter logic.
- Adjusted `handleSubmit` function signature in `create-order-form.tsx` to remove `event` parameter.
- **Refactor**: Renamed `handleAddToOrder` to `handleAddToOrderItems` and `handleSubmit` to `handleOrderCreation` in `create-order-form.tsx` for clearer separation of concerns.
- **Fix**: Prevented unintended page refreshes and premature API calls when adding items to the order list by:
  - Adding `onSubmit={e => e.preventDefault()}` to the form in `create-order-form.tsx`.
  - Explicitly setting `type="button"` for the "add" button in `order-search-results.tsx`. aligning with the button-triggered submission flow.

### Fixed
- Fixed pagination issue where applying advanced filters from pages other than the first half of pagination would cause the list to not show. Ensured filters reset pagination to page 1 and correctly display filtered items from IndexedDB.
- Fixed: Search functionality on paginated pages. Previously, searching on later pagination pages (e.g., page 56) would not display results correctly. This has been resolved by centralizing the `currentPage` state in the Zustand store, ensuring the search term is applied correctly within the `applyFilters` logic, and triggering filter re-application when the search term changes. This ensures consistent search results across all pagination pages.
- Removed the display of selected row count from the inventory data table.
- Ensured that clearing selected items in the dialog also unchecks them in the table.
- Enhanced the design of the inventory data table:
  - Header background color updated to match the sidebar and is fixed on hover.
  - Header font color changed to white and bold.
  - Vertical column separators have been removed.
  - Data table now has a card-like design with a bolder border and shadow.
  - Sorting removed for 'Category' and 'Power Type' columns.
  - Numerical values (SPH, CYL, AXIS, ADD) now consistently display with two decimal places, prefixed with a "+" sign for positive values, and handling null values by displaying an empty string. Quantity values are displayed as integers.

## 1.0.0 - 2024-07-30

### Added
- Implemented background data fetching for inventory and retailer data upon successful login.
- Removed direct data fetching from inventory pages.
- Initial project setup and basic dashboard structure.
- Refactored inventory filtering components:
  - Removed `InventoryFilter.tsx` component.
  - Integrated product code search directly into `src/app/dashboard/admin/inventory/page.tsx`.
  - `AdvancedFilter.tsx` is now the primary filter, and product code search operates on its results.
  - Item count and pagination are now based on the `AdvancedFilter`'s results.
  - Updated `useInventoryStore.ts` to support the new two-stage filtering logic.
- Added display of filtered inventory item count.
- Improved offline filtering experience: The application now uses cached data from IndexedDB without showing a "no internet connection" error if a network fetch fails.
- Added a toggle button to the inventory data table to display/hide spherical, cylindrical, axis, add, and lens side values.
- Enhanced `AdvancedFilter` component for improved search capability:
  - Replaced dropdowns with typeable input fields for `spherical_value`, `cylindrical_value`, `add_value`, and `axis_value`.
  - Implemented `Combobox` for `product_category` and `power_type` with auto-capitalization for category and keyword suggestions.
  - Modified `lens_side` filter to offer 'Left', 'Right', or 'None' selection.
  - Updated `inventoryStore` to align filtering logic with new input types.
- Updated IndexedDB schema to include `spherical_value`, `cylindrical_value`, `add_value`, `axis_value`, and `lens_side` as indexed fields for faster querying.
- Restructured `lens_side` field in `src/database/schema.ts` to accept only 'left', 'right', or `null` values.
- Updated `AdvancedFilter` component (`src/components/inventory_list/AdvancedFilter.tsx`) to remove 'N/A' as a selectable option for `lens_side` and handle `null` as the default/all state.
- Modified `inventoryStore` (`src/lib/store/inventoryStore.ts`) to align `selectedLensSide` type with the updated schema and adjust filtering logic to correctly handle `null` values.
- Fixed `lens_side` filter case sensitivity by converting values to lowercase in `src/lib/store/inventoryStore.ts` and `src/components/inventory_list/AdvancedFilter.tsx`.
- Enhanced JSDoc comments and TypeScript types for improved code clarity and maintainability in `src/lib/store/inventoryStore.ts`, `src/components/inventory_list/AdvancedFilter.tsx`, and `src/components/ui/Combobox.tsx`.
- Implemented reception order page with tabs for all, active, pending, completed, and cancelled orders.
- Updated order schema, API calls, and column definitions to include `retailer_display_name`, `issued_by_display_name`, `dispatched_by_display_name`, and `delivered_by_display_name` fields.
- Integrated retailer data from Zustand store into `CreateOrderForm` and `RetailerCombobox` for dynamic retailer selection.

- Implemented filtering for `product_category` and `power_type` in the inventory list.
- Added "Show Lens Parameters" button to `src/app/dashboard/admin/inventory/page.tsx` to trigger on-demand fetching of lens parameters.
- Integrated `fetchLensParametersForPage` action in `src/lib/store/inventoryStore.ts` for lens parameter caching and inventory item updates.
- Initial setup of inventory management system with product code, quantity, and lens parameters (SPH, CYL, AXIS, ADD, Lens Side).
- Integrated Supabase for data storage and authentication.
- User authentication (sign-up, sign-in, sign-out).
- Dashboard for inventory management.

### Changed

- Removed pagination and filter logic from `data-table.tsx`.

### Fixed

- Implemented error handling and display in the login form (`src/app/login/page.tsx`).
- Improved offline data handling for inventory in `src/lib/store/inventoryStore.ts` to prioritize cached data from IndexedDB and only display network errors if no cached data is available.
- Removed "Show Lens Parameters for Current Page" button and its related functionality from `src/app/dashboard/admin/inventory/page.tsx`.
- Fixed TypeScript issues in `src/lib/api/supabase.ts` related to `fetchProductData` return types and query structure.
- Implemented querying the total number of rows in the `products` table to optimize batched fetching.
- Implemented batched fetching of all product data from Supabase and storing them locally in IndexedDB.

- Fixed `ConstraintError` in IndexedDB by changing `bulkAdd` to `bulkPut` for inventory items.
```
- Resolved issues with data display in inventory table.

## [0.1.1] - 2023-10-28
### Added
- **Retailer Data Management:** Implemented bulk fetching of retailer records, schema storage, and offline search using Zustand and local storage. Integrated retailer selection into order creation form with searchable dropdown. Updated database schema and API calls to reflect `retailer_id` and join `retailer_name`.

## [0.1.0] - 2023-10-27
### Added
- Initial project setup.
- Fixed TypeScript error related to `setSelectedProducts` in `useInventoryStore` by ensuring `InventoryItem` was correctly imported and by adding JSDoc comments to `inventoryStore.ts`.
- Added "View Selected Items" button and dialog to display selected products with detailed descriptions (Product ID, Description, Quantity). Product description includes Category, Power Type, SPH, CYL, ADD, AXIS, and Lens Side, omitting missing values. The button is now only visible when items are selected.
- Added a "Clear Selected Items" button within the selected items dialog to clear all selected products.
- User authentication.
- Basic dashboard.
- Inventory management.
- Order management.


## [0.1.1] - 2024-07-30

### Added

### Changed
- Updated database schema to replace `customer_id` with `retailer_id` (UUID) in the `orders` table, establishing a foreign key relationship with `retailer.retailer_id`.
- Removed `customer_display_name` from `src/types/order.ts` and updated `customer_id` to refer to `retailer_id`.
- Modified API functions in `src/lib/api/orders.ts` to fetch `retailer_name` by joining with the `retailer` table using `retailer_id`.
- Updated `src/components/orders/columns.tsx` to display `retailer.retailer_name` and changed column header from 'Customer' to 'Retailer'.
- Removed the 'Customers' section from the admin dashboard and all related files and navigation links.
```