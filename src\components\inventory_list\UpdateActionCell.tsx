import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { InventoryActionDialog } from "./InventoryActionDialog";
import { InventoryItem } from "@/database/schema";

interface UpdateActionCellProps {
  inventoryItem: InventoryItem;
}

export const UpdateActionCell: React.FC<UpdateActionCellProps> = ({ inventoryItem }) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button variant="ghost" size="icon" onClick={() => setOpen(true)}>
        Edit
      </Button>
      <InventoryActionDialog product={inventoryItem} isOpen={open} onClose={() => setOpen(false)} />
    </>
  );
};
