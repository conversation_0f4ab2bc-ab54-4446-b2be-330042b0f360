import { createClient } from '@supabase/supabase-js';
import { Retailer } from '../types/retailer';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase URL or Anon Key environment variables.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Fetches all retailers from the 'retailer' table.
 * @returns {Promise<Retailer[] | null>} A promise that resolves to an array of Retailer objects or null if an error occurs.
 * @throws {Error} If there is an error fetching the data.
 */
export async function fetchAllRetailers(): Promise<Retailer[] | null> {
  const { data, error } = await supabase.from('retailer').select('*');

  if (error) {
    console.error('Error fetching retailers:', error.message);
    throw new Error(`Failed to fetch retailers: ${error.message}`);
  }
  return data as Retailer[];
}