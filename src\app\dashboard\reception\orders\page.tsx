"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useOrdersStore } from "@/lib/store/ordersStore";
import { Order } from "@/types/order";
import { DataTable } from "@/components/orders/data-table";
import { OrderDetailDrawer } from "@/components/orders/OrderDetailDrawer";
import { fetchOrderBillItems } from "@/lib/api/fetchOrderBillItems";
import { withActionsColumn } from "@/components/orders/columns";
import { OrderBillItem } from "@/types/order-bill-item";
import { CreateOrderDialog } from "@/components/orders/create-order-dialog";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";

const OrdersPage = () => {
  const {
    allOrders,
    pendingOrders,
    activeOrders,
    completedOrders,
    cancelledOrders,
    totalCounts,
    currentPage,
    pageSize,
    isInitialLoading,
    isLoadingMore,
    error,
    fetchInitialOrders,
    loadMoreOrders,
    setCurrentPage,
    refreshOrders,
  } = useOrdersStore();

  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [selectedOrder, setSelectedOrder] = React.useState<Order | null>(null);
  const [billItems, setBillItems] = React.useState<OrderBillItem[]>([]);
  const [refreshing, setRefreshing] = React.useState(false);

  // Fetch initial orders on component mount
  React.useEffect(() => {
    fetchInitialOrders();
  }, [fetchInitialOrders]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshOrders();
    } finally {
      setRefreshing(false);
    }
  };

  const handleViewOrder = async (order: Order) => {
    setSelectedOrder(order);
    setDrawerOpen(true);
    const items = await fetchOrderBillItems(order.order_id);
    setBillItems(items);
  };

  const allOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "order_status", header: "Status" },
    { accessorKey: "order_location", header: "Order Location" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "created_at", header: "Created At" },
  ], handleViewOrder, handleRefresh);
  const activeOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "dispatched_at", header: "Dispatched At" },
    { accessorKey: "order_location", header: "Order Location" },
    { accessorKey: "created_at", header: "Created At" },
  ], handleViewOrder, handleRefresh);
  const pendingOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "order_location", header: "Order Location" },
    { accessorKey: "created_at", header: "Created At" },
  ], handleViewOrder, handleRefresh);
  const completedOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "created_at", header: "Created At" },
    { accessorKey: "dispatched_at", header: "Dispatched At" },
    { accessorKey: "completed_at", header: "Completed At" },
    { accessorKey: "order_location", header: "Order Location" },
  ], handleViewOrder, handleRefresh);
  const cancelledOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "cancelled_by", header: "Cancelled By" },
    { accessorKey: "cancel_reason", header: "Cancel Reason" },
    { accessorKey: "created_at", header: "Created At" },
    { accessorKey: "cancelled_at", header: "Cancelled At" },
    { accessorKey: "order_location", header: "Order Location" },
  ], handleViewOrder, handleRefresh);

  if (isInitialLoading && allOrders.length === 0) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading orders...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-4">Error loading orders: {error}</p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2 mb-6">
        <h2 className="text-3xl font-bold tracking-tight">Orders</h2>
        <div className="flex items-center gap-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <CreateOrderDialog />
        </div>
      </div>
      {/* Add spacing between header and tabs */}
      <div className="mb-6" />
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid grid-cols-5">
          <TabsTrigger value="all">All Orders ({totalCounts.all})</TabsTrigger>
          <TabsTrigger value="pending">Pending Orders ({totalCounts.pending})</TabsTrigger>
          <TabsTrigger value="active">Active Orders ({totalCounts.active})</TabsTrigger>
          <TabsTrigger value="completed">Completed Orders ({totalCounts.completed})</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled Orders ({totalCounts.cancelled})</TabsTrigger>
        </TabsList>
        <TabsContent value="all">
          <DataTable columns={allOrdersColumns} data={allOrders} />
          {allOrders.length < totalCounts.all && (
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => loadMoreOrders('all')}
                disabled={isLoadingMore}
                variant="outline"
              >
                {isLoadingMore ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  `Load More (${allOrders.length} of ${totalCounts.all})`
                )}
              </Button>
            </div>
          )}
        </TabsContent>
        <TabsContent value="pending">
          <DataTable columns={pendingOrdersColumns} data={pendingOrders} />
          {pendingOrders.length < totalCounts.pending && (
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => loadMoreOrders('pending')}
                disabled={isLoadingMore}
                variant="outline"
              >
                {isLoadingMore ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  `Load More (${pendingOrders.length} of ${totalCounts.pending})`
                )}
              </Button>
            </div>
          )}
        </TabsContent>
        <TabsContent value="active">
          <DataTable columns={activeOrdersColumns} data={activeOrders} />
          {activeOrders.length < totalCounts.active && (
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => loadMoreOrders('active')}
                disabled={isLoadingMore}
                variant="outline"
              >
                {isLoadingMore ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  `Load More (${activeOrders.length} of ${totalCounts.active})`
                )}
              </Button>
            </div>
          )}
        </TabsContent>
        <TabsContent value="completed">
          <DataTable columns={completedOrdersColumns} data={completedOrders} />
          {completedOrders.length < totalCounts.completed && (
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => loadMoreOrders('completed')}
                disabled={isLoadingMore}
                variant="outline"
              >
                {isLoadingMore ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  `Load More (${completedOrders.length} of ${totalCounts.completed})`
                )}
              </Button>
            </div>
          )}
        </TabsContent>
        <TabsContent value="cancelled">
          <DataTable columns={cancelledOrdersColumns} data={cancelledOrders} />
          {cancelledOrders.length < totalCounts.cancelled && (
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => loadMoreOrders('cancelled')}
                disabled={isLoadingMore}
                variant="outline"
              >
                {isLoadingMore ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  `Load More (${cancelledOrders.length} of ${totalCounts.cancelled})`
                )}
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
      <OrderDetailDrawer
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        order={selectedOrder}
        billItems={billItems}
      />
    </div>
  );
};

export default OrdersPage;