"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  fetchAllOrders,
  fetchActiveOrders,
  fetchPendingOrders,
  fetchCompletedOrders,
  fetchCancelledOrders,
} from "@/lib/api/orders";
import { Order } from "@/types/order";
import { DataTable } from "@/components/orders/data-table";
import { OrderDetailDrawer } from "@/components/orders/OrderDetailDrawer";
import { fetchOrderBillItems } from "@/lib/api/fetchOrderBillItems";
import { withActionsColumn } from "@/components/orders/columns";
import { OrderBillItem } from "@/types/order-bill-item";
import { CreateOrderDialog } from "@/components/orders/create-order-dialog";

const OrdersPage = () => {
  const [allOrders, setAllOrders] = React.useState<Order[]>([]);
  const [activeOrders, setActiveOrders] = React.useState<Order[]>([]);
  const [pendingOrders, setPendingOrders] = React.useState<Order[]>([]);
  const [completedOrders, setCompletedOrders] = React.useState<Order[]>([]);
  const [cancelledOrders, setCancelledOrders] = React.useState<Order[]>([]);
  const [drawerOpen, setDrawerOpen] = React.useState(false);
  const [selectedOrder, setSelectedOrder] = React.useState<Order | null>(null);
  const [billItems, setBillItems] = React.useState<OrderBillItem[]>([]);

  const refreshOrders = async () => {
    setAllOrders(await fetchAllOrders());
    setActiveOrders(await fetchActiveOrders());
    setPendingOrders(await fetchPendingOrders());
    setCompletedOrders(await fetchCompletedOrders());
    setCancelledOrders(await fetchCancelledOrders());
  };

  React.useEffect(() => {
    const loadOrders = async () => {
      setAllOrders(await fetchAllOrders());
      setActiveOrders(await fetchActiveOrders());
      setPendingOrders(await fetchPendingOrders());
      setCompletedOrders(await fetchCompletedOrders());
      setCancelledOrders(await fetchCancelledOrders());
    };
    loadOrders();
  }, []);

  const handleViewOrder = async (order: Order) => {
    setSelectedOrder(order);
    setDrawerOpen(true);
    const items = await fetchOrderBillItems(order.order_id);
    setBillItems(items);
  };

  const allOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "order_status", header: "Status" },
    { accessorKey: "order_location", header: "Order Location" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "created_at", header: "Created At" },
  ], handleViewOrder, refreshOrders);
  const activeOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "dispatched_at", header: "Dispatched At" },
    { accessorKey: "order_location", header: "Order Location" },
    { accessorKey: "created_at", header: "Created At" },
  ], handleViewOrder, refreshOrders);
  const pendingOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "order_location", header: "Order Location" },
    { accessorKey: "created_at", header: "Created At" },
  ], handleViewOrder, refreshOrders);
  const completedOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "created_at", header: "Created At" },
    { accessorKey: "dispatched_at", header: "Dispatched At" },
    { accessorKey: "completed_at", header: "Completed At" },
    { accessorKey: "order_location", header: "Order Location" },
  ], handleViewOrder, refreshOrders);
  const cancelledOrdersColumns = withActionsColumn([
    { accessorKey: "order_code", header: "Order Code" },
    { accessorKey: "retailer_display_name", header: "Retailer" },
    { accessorKey: "issued_by_display_name", header: "Issued By" },
    { accessorKey: "dispatched_by_display_name", header: "Dispatched By" },
    { accessorKey: "delivered_by_display_name", header: "Delivered By" },
    { accessorKey: "cancelled_by", header: "Cancelled By" },
    { accessorKey: "cancel_reason", header: "Cancel Reason" },
    { accessorKey: "created_at", header: "Created At" },
    { accessorKey: "cancelled_at", header: "Cancelled At" },
    { accessorKey: "order_location", header: "Order Location" },
  ], handleViewOrder, refreshOrders);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2 mb-6">
        <h2 className="text-3xl font-bold tracking-tight">Orders</h2>
        <div className="flex items-center gap-2">
          {/* Create Order button on top right */}
          <CreateOrderDialog />
        </div>
      </div>
      {/* Add spacing between header and tabs */}
      <div className="mb-6" />
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid grid-cols-5">
          <TabsTrigger value="all">All Orders ({allOrders.length})</TabsTrigger>
          <TabsTrigger value="pending">Pending Orders ({pendingOrders.length})</TabsTrigger>
          <TabsTrigger value="active">Active Orders ({activeOrders.length})</TabsTrigger>
          <TabsTrigger value="completed">Completed Orders ({completedOrders.length})</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled Orders ({cancelledOrders.length})</TabsTrigger>
        </TabsList>
        <TabsContent value="all">
          <DataTable columns={allOrdersColumns} data={allOrders} />
        </TabsContent>
        <TabsContent value="pending">
          <DataTable columns={pendingOrdersColumns} data={pendingOrders} />
        </TabsContent>
        <TabsContent value="active">
          <DataTable columns={activeOrdersColumns} data={activeOrders} />
        </TabsContent>
        <TabsContent value="completed">
          <DataTable columns={completedOrdersColumns} data={completedOrders} />
        </TabsContent>
        <TabsContent value="cancelled">
          <DataTable columns={cancelledOrdersColumns} data={cancelledOrders} />
        </TabsContent>
      </Tabs>
      <OrderDetailDrawer
        open={drawerOpen}
        onOpenChange={setDrawerOpen}
        order={selectedOrder}
        billItems={billItems}
      />
    </div>
  );
};

export default OrdersPage;