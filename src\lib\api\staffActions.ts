import { supabase } from '@/lib/supabaseClient';

export async function updateStaffMember({
  user_id,
  full_name,
  staff_role,
  phone_number,
  address,
}: {
  user_id: string;
  full_name: string;
  staff_role: string;
  phone_number: string;
  address: string;
}): Promise<{ success: boolean; error?: string; debug?: unknown }> {
  // Debug log: show what is being updated
  console.log('[updateStaffMember] Updating user_id:', user_id, 'with data:', { full_name, staff_role, phone_number, address });
  const { data, error } = await supabase
    .from('staff_members')
    .update({ full_name, staff_role, phone_number, address })
    .eq('user_id', user_id)
    .select();
  if (error) return { success: false, error: error.message, debug: { user_id, full_name, staff_role, phone_number, address } };
  if (!data || data.length === 0) {
    return { success: false, error: 'No staff member updated. Check user_id.', debug: { user_id, full_name, staff_role, phone_number, address } };
  }
  return { success: true, debug: { updated: data[0] } };
}

export async function deleteStaffMember(user_id: string): Promise<{ success: boolean; error?: string }> {
  const { error } = await supabase
    .from('staff_members')
    .delete()
    .eq('user_id', user_id);
  if (error) return { success: false, error: error.message };
  return { success: true };
}
