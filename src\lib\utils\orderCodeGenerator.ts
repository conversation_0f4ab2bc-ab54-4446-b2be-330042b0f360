/**
 * Generates a unique order code based on the current date, time, and a random 4-digit number.
 * The format is `ORD-YYYYMMDD-HHMMSS[A/P]`.
 * @returns {string} The generated order code.
 */
export function generateOrderCode(): string {
  const now = new Date(); // This creates a fresh timestamp each call

  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');

  let hours = now.getHours();
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const ampm = hours >= 12 ? 'P' : 'A';
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  const formattedHours = hours.toString().padStart(2, '0');

  return `ORD-${year}${month}${day}-${formattedHours}${minutes}${seconds}${ampm}`;
}