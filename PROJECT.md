# Project: HisabKitab App

## ✅ Completed Features
- Removed role selection from the login form.
- Initialized Supabase client.
- Installed Supabase packages (`@supabase/supabase-js`, `@supabase/ssr`).
- Created `.env.local` for Supabase API keys.
- Implemented Supabase authentication in `src/app/login/page.tsx`.
- Refactored Supabase client to `src/lib/supabaseClient.ts`.
- Created `src/lib/api` directory and `src/lib/api/auth.ts` for authentication logic.
- Updated login page (`src/app/login/page.tsx`) to use new authentication API.
- Implemented client-side state management with Zustand for user and role information, including persistence.
- Implemented role-based dashboards with dynamic routing and shared layout.
- Established a new dashboard directory structure: `/dashboard`, `/dashboard/admin`, `/dashboard/reception`, `/dashboard/finance`.
- Created sub-dashboards for admin: `staff`, `vendors`, `inventory`, `finance/overview`, `finance/reports`, `settings`.
- Created sub-dashboards for reception: `orders`, `inventory`, `settings`.
- Created sub-dashboards for finance: `transactions`, `reports`, `settings`.
- Modified Data Table component in `src/components/inventory_list/data-table.tsx`:
  - Removed the generic column selector.
  - [x] Add toggle lens parameter to display spherical, cylindrical, axis, add and lens side values
- Integrated Data Table with dummy data into Admin Inventory page (`src/app/dashboard/admin/inventory/page.tsx`).
- Added 'quantity' column to inventory data table and updated dummy data.
- Configured role-based redirection for 'admin' to '/dashboard/admin/inventory' in `src/app/dashboard/page.tsx`.
- Created `src/components/ui/checkbox.tsx` for data table row selection.
- Installed `@radix-ui/react-checkbox` and `lucide-react`.
- Created `src/components/ui/dropdown-menu.tsx` for column visibility in data table.
- Installed `@radix-ui/react-dropdown-menu`.
- Fixed `inset` property errors in `src/components/ui/dropdown-menu.tsx`.
- Corrected `DotFilledIcon` import in `src/components/ui/dropdown-menu.tsx`.
- Prevented column visibility dropdown from closing on item selection.
- Removed pagination and filter logic from `src/components/inventory_list/data-table.tsx` to centralize data handling in the inventory page.
- Initiated refactoring of inventory data fetching and local storage to use Zustand for global state management and IndexedDB for offline persistence. See [INVENTORY_REFACTOR_PLAN.md](INVENTORY_REFACTOR_PLAN.md) for detailed plan.
- Refactored inventory data fetching to use Zustand and IndexedDB for product data, with on-demand batch fetching of lens parameters for the current page.
- Updated `InventoryItem` type in `src/database/schema.ts` to correctly type `sph`, `cyl`, `axis`, and `add` as `number | null`.

- Integrated `fetchLensParametersForPage` action in `src/lib/store/inventoryStore.ts` to manage lens parameter caching and updating inventory items.
- Added a "Show Lens Parameters" button to `src/app/dashboard/admin/inventory/page.tsx` to trigger on-demand fetching of lens parameters for displayed items.
- Removed `InventoryFilter` component.
- Integrated product code search directly into the inventory page.
- Established `AdvancedFilter` as the primary filter, with product code search applying to its results.
- Ensured item count and pagination are based on `AdvancedFilter` results.
- [x] Display count of filtered inventory items
- [x] Improved offline filtering experience (no 'no internet' error if cached data exists)
- [x] Fixed `ConstraintError` in IndexedDB by changing `bulkAdd` to `bulkPut` for inventory items.
- [x] Enhanced `AdvancedFilter` component for improved search capability:
  - Replaced dropdowns with typeable input fields for `spherical_value`, `cylindrical_value`, `add_value`, and `axis_value`.
  - Implemented `Combobox` for `product_category` and `power_type` with auto-capitalization for category and keyword suggestions.
  - Modified `lens_side` filter to offer 'Left', 'Right', or 'None' selection.
  - Updated `inventoryStore` to align filtering logic with new input types.
- [x] Updated IndexedDB schema to include `spherical_value`, `cylindrical_value`, `add_value`, `axis_value`, and `lens_side` as indexed fields for faster querying.
- [x] Restructured `lens_side` field in `src/database/schema.ts` to accept only 'left', 'right', or `null` values.
- [x] Updated `AdvancedFilter` component (`src/components/inventory_list/AdvancedFilter.tsx`) to remove 'N/A' as a selectable option for `lens_side` and handle `null` as the default/all state.
- [x] Modified `inventoryStore` (`src/lib/store/inventoryStore.ts`) to align `selectedLensSide` type with the updated schema and adjust filtering logic to correctly handle `null` values.
- [x] Fixed `lens_side` filter case sensitivity by converting values to lowercase in `src/lib/store/inventoryStore.ts` and `src/components/inventory_list/AdvancedFilter.tsx`.
- **Inventory Filtering Enhancements (JSDoc & Types)**: Enhanced JSDoc comments and strict TypeScript types for `inventoryStore.ts`, `AdvancedFilter.tsx`, and `Combobox.tsx` to improve code clarity and maintainability.
- **Inventory Filter UI/UX Improvements**: Corrected `div` nesting, removed duplicate elements, set step intervals for SPH, CYL, and ADD to 0.25, and AXIS to 90, re-added AXIS input, product code input, lens side select, and the "Apply Filters" button in `AdvancedFilter.tsx`. Implemented logic to treat 0 as null for SPH, CYL, ADD, and AXIS input fields in `AdvancedFilter.tsx`.
- [x] Added a "Clear Filters" button to `AdvancedFilter.tsx` and implemented `clearFilters` action in `inventoryStore.ts` to reset all advanced filter states.
- Fixed: Pagination issue where applying advanced filters did not reset pagination to page 1, leading to incorrect display of filtered items from IndexedDB. Now, applying filters correctly resets pagination to page 1 and displays the filtered items.
- Fixed: Search functionality on paginated pages. Previously, searching on later pagination pages (e.g., page 56) would not display results correctly. This has been resolved by centralizing the `currentPage` state in the Zustand store, ensuring the search term is applied correctly within the `applyFilters` logic, and triggering filter re-application when the search term changes. This ensures consistent search results across all pagination pages.
- Removed: The display of selected row count in `src/components/inventory_list/data-table.tsx` as it was deemed unnecessary for the current functionality.
- Enhanced data table design in `src/components/inventory_list/data-table.tsx` and `src/components/inventory_list/columns.tsx`:
  - Header background set to `bg-gray-800` with white, bold font.
  - Vertical column separators have been removed, maintaining a clean, continuous horizontal line for rows.
  - Table container now features a card-like design with shadow (`rounded-md border shadow`).
  - Sorting functionality removed for 'Category' and 'Power Type' columns.
  - Numerical values (SPH, CYL, AXIS, ADD) now consistently display with two decimal places, prefixed with a "+" sign for positive values, and handling null values by displaying an empty string.
  - Quantity values are displayed as integers.
  - "Export Selected Items" button renamed to "Export All Items".
  - "Show/Hide Lens Params" toggle button repositioned next to the "Export All Items" button.
- Improved offline data handling for inventory in `src/lib/store/inventoryStore.ts` to prioritize cached data from IndexedDB and only display network errors if no cached data is available.
- Removed "Show Lens Parameters for Current Page" button and its related functionality from `src/app/dashboard/admin/inventory/page.tsx`.
- Implemented error handling and display in the login form (`src/app/login/page.tsx`).
- Fixed TypeScript issues in `src/lib/api/supabase.ts` related to `fetchProductData` return types and query structure.
- Implemented querying the total number of rows in the `products` table to optimize batched fetching.
- Implemented batched fetching of all product data from Supabase and storing them locally in IndexedDB.
- Implemented pagination for inventory data, displaying 100 rows per page.
- Corrected `accessorKey` values for spherical, cylindrical, axis, add, and lens side in `src/components/inventory_list/columns.tsx` to match the `Product` schema.
- Updated `src/components/inventory_list/data-table.tsx` to use the corrected `accessorKey` values for lens parameters and removed the "Show Lens Parameters for current page" button.
- Corrected `accessorKey` for `powerType` column in `src/components/inventory_list/columns.tsx` from `power-type` to `power_type`.
- Corrected column ID for `category` in `src/components/inventory_list/data-table.tsx` from `category` to `product_category` to match schema.
- Corrected filter `accessorKey` for `power_type` in `src/components/inventory_list/data-table.tsx` from `power-type` to `power_type`.
- Implemented product data batch fetching and storing with Zustand upon successful login.
- Implemented retailer data batch fetching and storing with Zustand upon successful login.
- Removed data fetching from inventory page on selection.

## ✅ Completed Features
- Implemented product selection in inventory table with state persistence across pagination.
- **Refactor Quantity Handling**: Updated UI components (`columns.tsx`, `SelectedProductsDialog.tsx`) to display `quantity_left` instead of `quantity_total`. Modified `bulkUpdateProductQuantity` in `products.ts` to ensure `quantity_total` is used for historical recording only, while `quantity_left` and `quantity_added` are updated for current inventory.
- **Refactor `generateProductDescription`**: Moved `generateProductDescription` from `SelectedProductsDialog.tsx` to `src/lib/utils/productDescription.ts` for improved reusability and maintainability.
- **Implement Quantity Reduction**: Added `bulkReductProductQuantity` function to `src/lib/api/products.ts` to handle reduction of `quantity_left` and increment of `quantity_out` for selected products.
- **Product Search in Order Creation**: Further enhanced the product search section in <mcfile name="order-item-search.tsx" path="src/components/orders/order-item-search.tsx"></mcfile> to display detailed product information in the console when the simulated search returns 5 or fewer items.
- Implemented a reset option for the search filter in `order-item-search.tsx`.
- Added a dedicated section for displaying search results in `order-item-search.tsx`.
- Integrated actual inventory data filtering from `useInventoryStore` in `order-item-search.tsx`, replacing simulated results with real-time filtering based on product category, SPH, CYL, ADD, and AXIS values.
- Enhanced `order-item-search.tsx`:
  - Added `product_code` and `lens_side` as new search parameters.
  - Updated `handleSearch` to filter by these new parameters.
  - Refactored search results display from a list to a table with columns for product code, description, quantity left, and an "Add to List" action button.
  - Implemented logic to limit search results display to a maximum of 5 items, with a message indicating more results if applicable.
  - Added appropriate messages for empty search results based on whether a search was performed.
  - Fixed parsing error in conditional rendering logic.
  - Increased dialog width for `CreateOrderDialog` in `create-order-dialog.tsx` to `sm:max-w-[1200px]` for better product search and order item display.
  - Fixed parsing error in `order-item-search.tsx` due to an extra closing `</div>` tag.
  - Removed `lens_side` search parameter from `order-item-search.tsx`.
  - Implemented debounced search for `product_code` in `order-item-search.tsx` with a minimum of 5 characters.
  - Updated search results message in `order-item-search.tsx` to display the total number of results when truncated.
- Reordered search filters in `order-item-search.tsx` to prioritize product code, and adjusted the width of the search results list.
- Removed debounced search for SPH, CYL, ADD, and AXIS fields in `order-item-search.tsx`, ensuring searches for these fields are triggered only by the search button.
- Removed the product code length error message from `order-item-search.tsx`.
- Implemented a `hasSearched` state in `order-item-search.tsx` to control the visibility of the "No products found" message, ensuring it only appears after a search is performed.
- Adjusted the width of the product code input field and other search filter input fields (SPH, CYL, ADD, AXIS) in `order-item-search.tsx` to optimize layout and fit elements on the same row.
- Removed the `className` prop from the `Combobox` component in `order-item-search.tsx` to resolve a type error.
- **Refactor Order Display**: Replaced the single `OrderDisplaySections` component with two distinct components: `OrderSearchResults` and `OrderItemsList`. This provides a more modular and maintainable structure for displaying search results and order items.
- **`OrderSearchResults`**: A new component (`src/components/orders/order-search-results.tsx`) dedicated to displaying search results in an empty table with columns for Product Code, Product Description, Quantity Left, and Action.
- **`OrderItemsList` Enhancement**: The `OrderItemsList` component (`src/components/orders/order-items-list.tsx`) has been significantly enhanced to display actual order items. It now includes:
  - A serial number (S.No) column starting from 1.
  - Product descriptions generated using `generateProductDescription`.
  - An editable quantity field for each item.
  - Price per unit displayed, derived from `price_per_unit`.
  - Auto-calculated total for each item.
  - A subtotal section at the end of the list.
  - A "Clear Order List" button to reset the order.
- **`CreateOrderForm` Integration**: The `CreateOrderForm` (`src/components/orders/create-order-form.tsx`) now manages the state of order items, handling additions from search results, quantity changes, and clearing the list. It passes the necessary props to `OrderItemsList`. The `item_description` in the final order data is now correctly generated using `generateProductDescription`.
- **Schema Update**: Added `price_per_unit` to the `Product` type in `src/database/schema.ts` to correctly reflect product pricing.
- **`OrderSearchResults` Update**: The `OrderSearchResults` component (`src/components/orders/order-search-results.tsx`) has been updated to optimize the search results table layout. The "Action" column has been replaced with a compact plus icon for adding items to the order list, and column widths have been adjusted for better fit and readability.
- Increased the width of the order creation dialog to `1500px`.
- Removed the line separator from the main content area of the order creation form.
- Implemented product search logic in `OrderItemSearch` to return a limited list and total count.
- Enhanced `OrderSearchResults` to display search results dynamically and indicate when results exceed the display limit.
- Fixed TypeScript errors in `OrderSearchResults` by converting JSDoc `@typedef` to TypeScript `interface` and explicitly typing mapped items.
- Fixed TypeScript errors in `OrderItemSearch` by converting JSDoc `@typedef` to TypeScript `interface` and correcting `setSearchResults` type usage.
- Implemented console logging for product data when "Add to List" button is clicked in `OrderSearchResults`.
- Added confirmation dialog for delete action in `src/components/inventory_list/SelectedProductsDialog.tsx`.
- Refactored `AlertDialog` components from `SelectedProductsDialog.tsx` into a new reusable component file: `src/components/ui/alert-dialog.tsx`.
- Styled the "Confirm Delete" button in `SelectedProductsDialog.tsx` red.
- Repositioned the "Clear Selected List" button in `SelectedProductsDialog.tsx` to be below the "Export" and "Delete" buttons, and adjusted its width to span their combined width.
- Reorganized UI of bulk update/reduce fields and buttons, and moved 'Clear Selected Items' button in `SelectedProductsDialog.tsx`.
- Added the count of selected products to the "View Selected Items" button in `src/components/inventory_list/data-table.tsx`.
- UI for Quantity Reduction: Added input field and button for bulk quantity reduction in `SelectedProductsDialog.tsx`.
- Displayed count of selected products.
- Added "View Selected Items" button and dialog to display selected products with detailed descriptions, visible only when items are selected.
- Added a "Clear Selected Items" button within the dialog to clear all selected products.
- Ensured that clearing selected items in the dialog also unchecks them in the table.
- Console logged selected products for debugging.
- Implemented product code validation in `OrderItemSearch` to require a minimum of 5 characters for search and display an error message if the condition is not met.
- Modified `OrderItemSearch` so that the product code field no longer automatically triggers a search; search is now explicitly initiated by the "Search" button.
- Adjusted the width of the search results and order items sections in `CreateOrderForm` to 40% and 60% respectively.
- Added `className` prop to `OrderItemsList` and `OrderSearchResults` components to allow for external styling and layout control.
- Implemented scrollable order items list in `OrderItemsList` to prevent dialog box height from increasing, with a fixed height and consistent column widths, ensuring only vertical scrolling.
- Implemented staff full name display in header and order logs.
- Refactored order item list display (Clear Order List button, Subtotal column).
- Corrected type mismatch for `subtotal` in `create-order-form.tsx`.
- Defined and updated TypeScript types for `Order`, `OrderBill`, and `OrderBillItem` to match SQL schemas.
- Removed `console.log` statement from `create-order-form.tsx`'s `handleSubmit` function (previously removed, now confirmed and re-documented after re-introduction during debugging). 
- Refactored `create-order-form.tsx` to receive `orderCode` and `issuedBy` as props directly from `create-order-dialog.tsx`, removing unnecessary setter props.
- Implemented `createOrder` function in `src/lib/api/orders.ts` for submitting new orders to Supabase.
- Integrated `createOrder` function into `create-order-form.tsx`'s `handleSubmit` to persist new order data.
- Modified `order-items-list.tsx` to display the `orderCode` and removed the `console.log` from its "Create New Order" button.
- Updated `create-order-form.tsx` to pass the `orderCode` prop to `OrderItemsList`.
- Fixed import path for `useAuthStore` in `create-order-form.tsx`.
- [x] Implement order code generation with `ORD-YYYYMMDD-HHMMSS[A/P]` format.
- [x] Implement order creation and order bill items creation in `create-order-form.tsx` and `src/lib/api/orders.ts`.
- Refactored order creation trigger to "Create" button within `OrderItemsList` component.
- Prevented unintended page refreshes and API calls when adding items to the order list by:
  - Adding `onSubmit={e => e.preventDefault()}` to the form in `create-order-form.tsx`.
  - Setting `type="button"` for the "add" button in `order-search-results.tsx`.
  - Renamed `handleAddToOrder` to `handleAddToOrderItems` and `handleSubmit` to `handleOrderCreation` in `create-order-form.tsx` to better separate concerns.
- [x] Adjust `handleSubmit` signature in `create-order-form.tsx` to remove `event` parameter.
- [x] Resolved "Cannot find name 'onSubmitOrder'" TypeScript error in `order-items-list.tsx` by including `onSubmitOrder` in the component's prop destructuring.

## 🚧 Current Tasks
- Implement Supabase authentication (login).
- Implement role-based access control after successful authentication.
- Integrate role-based route protection using Next.js middleware.
- Integrated Shadcn UI components into Admin, Reception, and Finance dashboard pages.
- Created and integrated Sidebar and Header components using Shadcn UI.
- Fixed import and duplicated content in `src/app/dashboard/layout.tsx`.
- Corrected `useAuthStore` import paths in `Sidebar.tsx` and `Header.tsx`.
- Removed logout functionality from `Header.tsx` (logout is now exclusively in `Sidebar.tsx`).
- Fixed `logout` function implementation in `Sidebar.tsx` to use `clearAuth`.
- Refactored `Sidebar.tsx` to correctly place `handleLogout` and use `router` within the component.
- Implemented root page (`src/app/page.tsx`) redirection to `/login`.
- Created `src/app/layout.tsx` to define the root layout for the application.
- Implemented role-based redirection after successful login in `src/app/login/page.tsx` (corrected paths).
- Added `'use client'` directive to `src/components/Sidebar.tsx` to resolve `useRouter` error.
- Added `'use client'` directive to `src/components/Header.tsx` to resolve `useSyncExternalStore` error.
- Implemented dynamic sidebar navigation links based on user roles, correctly fetching role from `staff_members` table.
- Fixed logout button width and positioning in `src/components/Sidebar.tsx`.
- Ensured sidebar renders only after authentication state is hydrated, resolving `_hasHydrated` and `user` being null/undefined on initial load.
- [x] Implement reception order page with tabs for all, active, pending, completed, and cancelled orders.
- [x] Implement centralized API function for product creation (`src/lib/api/products.ts`).
- [x] Integrate IndexedDB persistence for new product data via Zustand store.
- Extracted product categories, power types, and lens sides into `src/lib/data/product_constants.ts` for better organization.
- **Retailer Data Management:** Implemented bulk fetching of retailer records from Supabase, stored with schema format for type safety, and enabled offline search capabilities using Zustand and local storage. Integrated retailer selection into order creation form with searchable dropdown. Updated database schema and API calls to reflect `retailer_id` and join `retailer_name`.
- [x] Update `src/components/orders/columns.tsx` to reflect new display name fields.
- [x] Integrate retailer data from Zustand store into `CreateOrderForm` and `RetailerCombobox`.
## 📅 Future Roadmap
- User registration with Supabase.
- Implement staff member profile management.
- Integrate with other Supabase services (Storage, Realtime).
- Build out core application features based on roles.
- [ ] Implement inventory sorting
- [ ] Add inventory item details view/edit
- [ ] Integrate product images with Supabase Storage
- [ ] User management (roles, permissions)
- [ ] Sales tracking and reporting
- [ ] Purchase order management

## ⚠️ Known Issues & Solutions
- Remember to populate `.env.local` with actual Supabase URL and Anon Key.

### ✅ Completed Features