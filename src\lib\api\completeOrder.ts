import { Order } from '@/types/order';

export async function completeOrder(order: Order): Promise<{ success: boolean; error?: string }> {
  try {
    const res = await fetch('/api/orders/complete', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ orderId: order.order_id }),
    });
    if (!res.ok) {
      const err = await res.json();
      return { success: false, error: err.error || res.statusText };
    }
    return { success: true };
  } catch (e: any) {
    return { success: false, error: e?.message || String(e) };
  }
}
