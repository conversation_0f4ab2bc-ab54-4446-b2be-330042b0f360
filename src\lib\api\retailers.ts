import { supabase } from '@/lib/supabaseClient';
import axios from 'axios';

export async function createRetailer({
  retailer_name,
  retailer_location,
  retailer_number,
}: {
  retailer_name: string;
  retailer_location: string;
  retailer_number: string;
}): Promise<{ success: boolean; error?: string }> {
  const { error } = await supabase
    .from('retailer')
    .insert({ retailer_name, retailer_location, retailer_number });
  if (error) return { success: false, error: error.message };
  return { success: true };
}

export async function updateRetailer({
  retailer_id,
  retailer_name,
  retailer_location,
  retailer_number,
}: {
  retailer_id: string;
  retailer_name: string;
  retailer_location: string;
  retailer_number: string;
}): Promise<{ success: boolean; error?: string; updated?: unknown }> {
  try {
    const res = await axios.post('/api/retailer/update', {
      retailer_id,
      retailer_name,
      retailer_location,
      retailer_number,
    });
    return res.data;
  } catch (err: unknown) {
    return { success: false, error: (isAxiosError(err) ? err.response?.data?.error : (err as Error).message) };
  }
}

export async function deleteRetailer(retailer_id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const res = await axios.post('/api/retailer/delete', { retailer_id });
    return res.data;
  } catch (err: unknown) {
    return { success: false, error: (isAxiosError(err) ? err.response?.data?.error : (err as Error).message) };
  }
}

function isAxiosError(err: unknown): err is { response: { data: { error?: string } } } {
  return (
    typeof err === 'object' &&
    err !== null &&
    'response' in err &&
    typeof (err as { response?: unknown }).response === 'object' &&
    (err as { response?: unknown }).response !== null &&
    'data' in (err as { response: { data?: unknown } }).response
  );
}
