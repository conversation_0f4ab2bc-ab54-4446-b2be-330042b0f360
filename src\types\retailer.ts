/**
 * @typedef {Object} Retailer
 * @property {string} retailer_id - The unique identifier for the retailer (UUID).
 * @property {string} retailer_name - The name of the retailer.
 * @property {string | null} retailer_location - The location of the retailer (optional).
 * @property {string | null} retailer_number - The contact number of the retailer (optional).
 */
export type Retailer = {
  retailer_id: string;
  retailer_name: string;
  retailer_location: string | null;
  retailer_number: string | null;
};