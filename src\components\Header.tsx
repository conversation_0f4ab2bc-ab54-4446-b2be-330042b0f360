"use client";
import React from "react";
import { useAuthStore } from "@/lib/store/authStore";

/**
 * Header component for the dashboard.
 * Displays the logged-in user's email.
 * Uses Shadcn UI for styling.
 */
const Header: React.FC = () => {
  const { user, fullName } = useAuthStore();

  // user is now typed as User | null
  return (
    <header className="flex items-center justify-between p-4 bg-white shadow-md">
      <h1 className="text-xl font-semibold">Dashboard</h1>
      <div className="flex items-center space-x-4">
        {user && (
          <span className="text-gray-700">Welcome, {fullName || ''}</span>
        )}
      </div>
    </header>
  );
};

export default Header;