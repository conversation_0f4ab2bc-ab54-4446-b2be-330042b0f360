import { supabase } from '@/lib/supabaseClient';
import { Product } from '@/database/schema';



/**
 * Inserts a new product into the database.
 * @param productData - The data for the new product.
 * @returns A promise that resolves with the new product data or rejects with an error.
 */
export async function createProduct(productData: Omit<Product, 'product_id' | 'entry_date'>): Promise<Product | null> {
  const { data, error } = await supabase
    .from('products')
    .insert([{
      ...productData,
      entry_date: new Date().toISOString(),
    }])
    .select();

  if (error) {
    console.error('Error creating product:', error);
    throw error;
  }
  return data ? data[0] : null;
}

/**
 * Fetches all products from the database.
 * @returns A promise that resolves with an array of products or rejects with an error.
 */
export async function fetchProducts(): Promise<Product[]> {
  const { data, error } = await supabase
    .from('products')
    .select('*');

  if (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
  return data as Product[];
}

/**
 * Fetches products by their IDs from the database.
 * @param {string[]} productIds - An array of product IDs to fetch.
 * @returns {Promise<Product[]>} A promise that resolves with the products or rejects with an error.
 */
export async function fetchProductsByIds(productIds: string[]): Promise<Product[]> {
  if (!productIds.length) return [];
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .in('product_id', productIds);
  if (error) {
    console.error('Error fetching products by IDs:', error);
    throw error;
  }
  return data as Product[];
}

/**
 * Bulk updates the quantity of multiple products.
 * @param {string[]} productIds - An array of product IDs to update.
 * @param {number} quantityToAdd - The quantity to add to the existing quantity_left for each product.
 * @returns {Promise<Product[]>} A promise that resolves with the updated product data or rejects with an error.
 */
export async function bulkUpdateProductQuantity(productIds: string[], quantityToAdd: number): Promise<Product[]> {
  // Fetch current quantities for the products
  const { data: currentProducts, error: fetchError } = await supabase
    .from('products')
    .select('product_id, product_code, quantity_total, quantity_added, quantity_left, quantity_reserved')
    .in('product_id', productIds);

  if (fetchError) {
    console.error('Error fetching products for bulk update:', fetchError);
    throw fetchError;
  }

  const updates = (currentProducts || []).map(product => ({
    product_id: product.product_id,
    product_code: product.product_code, // ensure this is included
    quantity_left: (product.quantity_left || 0) + quantityToAdd,
    updated_at: new Date().toISOString(),
  }));

  const { data: updatedData, error: updateError } = await supabase
    .from('products')
    .upsert(updates, { onConflict: 'product_id' })
    .select();

  if (updateError) {
    console.error('Error updating product quantities:', updateError);
    throw updateError;
  }
  return updatedData as Product[];
}

/**
 * Bulk reduces the quantity of multiple products.
 * @param {string[]} productIds - An array of product IDs to update.
 * @param {number} quantityToReduce - The quantity to reduce from quantity_left and add to quantity_out for each product.
 * @returns {Promise<Product[]>} A promise that resolves with the updated product data or rejects with an error.
 */
export async function bulkReductProductQuantity(productIds: string[], quantityToReduce: number): Promise<Product[]> {
  // Fetch current quantities for the products
  const { data: currentProducts, error: fetchError } = await supabase
    .from('products')
    .select('product_id, product_code, quantity_out, quantity_left, quantity_reserved')
    .in('product_id', productIds);

  if (fetchError) {
    console.error('Error fetching current product quantities for reduction:', fetchError);
    throw fetchError;
  }

  if (!currentProducts) {
    return [];
  }

  // Calculate new quantities for quantity_out and quantity_left
  const updates = currentProducts.map(product => {
    const quantity_left = (product.quantity_left || 0) - quantityToReduce;
    return {
      product_id: product.product_id,
      product_code: product.product_code,
      quantity_out: (product.quantity_out || 0) + quantityToReduce,
      quantity_left,
      updated_at: new Date().toISOString(),
    };
  });

  // Perform the upsert with the calculated new quantities
  const { data, error } = await supabase
    .from('products')
    .upsert(updates, { onConflict: 'product_id' })
    .select();

  if (error) {
    console.error('Error bulk reducing product quantities:', JSON.stringify(error));
    throw error;
  }
  return data as Product[];
}

/**
 * Bulk deletes multiple products from the database.
 * @param {string[]} productIds - An array of product IDs to delete.
 * @returns {Promise<void>} A promise that resolves when the products are deleted or rejects with an error.
 */
export async function bulkDeleteProducts(productIds: string[]): Promise<void> {
  const { error } = await supabase
    .from('products')
    .delete()
    .in('product_id', productIds);

  if (error) {
    console.error('Error bulk deleting products:', error);
    throw error;
  }
}

/**
 * Updates a product in the database.
 * @param productData - The updated product data.
 * @returns A promise that resolves with the updated product data or rejects with an error.
 */
export async function updateProduct(productData: Partial<Product> & { product_id: string }): Promise<Product> {
  const { data, error } = await supabase
    .from('products')
    .update({ ...productData, updated_at: new Date().toISOString() })
    .eq('product_id', productData.product_id)
    .select();
  if (error) {
    console.error('Error updating product:', error);
    throw error;
  }
  if (!data || !data[0]) throw new Error('No product returned after update');
  return data[0] as Product;
}

/**
 * Deletes a single product from the database.
 * @param {string} productId - The product ID to delete.
 * @returns {Promise<void>} A promise that resolves when the product is deleted or rejects with an error.
 */
export async function deleteProduct(productId: string): Promise<void> {
  const { error } = await supabase
    .from('products')
    .delete()
    .eq('product_id', productId);
  if (error) {
    console.error('Error deleting product:', error);
    throw error;
  }
}