'use client';
import { useEffect, useState } from "react";
import { useRetailerStore } from '@/lib/store/retailerStore';
import { Retailer } from "@/database/schema";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { createRetailer, updateRetailer, deleteRetailer } from "@/lib/api/retailers";

const PAGE_SIZE = 20;

function RetailerDialog({ onCreated, initial, mode, onClose }: {
  onCreated: () => void;
  initial?: Partial<Retailer>;
  mode: 'create' | 'edit';
  onClose?: () => void;
}) {
  const updateRetailerInStore = useRetailerStore(state => state.updateRetailerInStore);
  const [open, setOpen] = useState(false);
  const [form, setForm] = useState({
    retailer_name: initial?.retailer_name || "",
    retailer_location: initial?.retailer_location || "",
    retailer_number: initial?.retailer_number || "",
  });
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && initial) {
      setForm({
        retailer_name: initial.retailer_name || "",
        retailer_location: initial.retailer_location || "",
        retailer_number: initial.retailer_number || "",
      });
    }
  }, [open, initial]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm(f => ({ ...f, [e.target.name]: e.target.value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccess(false);
    setError(null);
    let res;
    if (mode === 'edit' && initial?.retailer_id) {
      res = await updateRetailer({ retailer_id: initial.retailer_id, ...form });
    } else {
      res = await createRetailer(form);
    }
    setLoading(false);
    if (res.success) {
      setSuccess(true);
      if (mode === 'edit' && initial?.retailer_id) {
        updateRetailerInStore({ retailer_id: initial.retailer_id, ...form });
      }
      onCreated();
      setTimeout(() => { setOpen(false); onClose?.(); }, 1000);
    } else {
      setError(res.error || "Failed to save retailer");
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button onClick={() => setOpen(true)} className="mb-4">
        {mode === 'edit' ? 'Edit' : '+ New Retailer'}
      </Button>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>{mode === 'edit' ? 'Edit Retailer' : 'Add New Retailer'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="retailer_name" placeholder="Retailer Name" value={form.retailer_name} onChange={handleChange} required />
          <Input name="retailer_location" placeholder="Location" value={form.retailer_location} onChange={handleChange} required />
          <Input name="retailer_number" placeholder="Number" value={form.retailer_number} onChange={handleChange} required />
          {error && <div className="text-red-500 text-sm">{error}</div>}
          {success && <div className="text-green-600 text-sm">Retailer saved!</div>}
          <DialogFooter>
            <Button type="submit" disabled={loading}>{loading ? "Saving..." : (mode === 'edit' ? 'Update' : 'Create')}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default function AdminRetailersPage() {
  const { retailers, fetchRetailers, deleteRetailerFromStore } = useRetailerStore();
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [hardRefreshLoading, setHardRefreshLoading] = useState(false);
  const [hardRefreshError, setHardRefreshError] = useState<string | null>(null);

  // Sorting state
  const [sortAsc, setSortAsc] = useState(true);
  const [sortBy, setSortBy] = useState<'retailer_name' | 'retailer_location' | 'retailer_number'>('retailer_name');

  // Fetch retailers if not loaded
  useEffect(() => {
    if (retailers.length === 0) {
      fetchRetailers();
    }
  }, [retailers.length, fetchRetailers]);

  // Set loading to false as soon as retailers data is available
  useEffect(() => {
    if (retailers.length > 0) {
      setLoading(false);
    }
  }, [retailers.length]);

  // Sort retailers before paginating
  const sortedRetailers = [...retailers].sort((a, b) => {
    const aVal = (a[sortBy] || '').toString().toLowerCase();
    const bVal = (b[sortBy] || '').toString().toLowerCase();
    if (aVal < bVal) return sortAsc ? -1 : 1;
    if (aVal > bVal) return sortAsc ? 1 : -1;
    return 0;
  });
  const totalPages = Math.ceil(retailers.length / PAGE_SIZE);
  const paginatedRetailers = sortedRetailers.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

  const handleHardRefresh = async () => {
    setHardRefreshLoading(true);
    setHardRefreshError(null);
    try {
      await fetchRetailers(true); // forceRefresh = true
    } catch (e: unknown) {
      setHardRefreshError((e as Error).message || 'Failed to refresh retailers');
    }
    setHardRefreshLoading(false);
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">Admin Retailer Management</h2>
        <Button variant="outline" onClick={handleHardRefresh} disabled={hardRefreshLoading}>
          {hardRefreshLoading ? 'Refreshing...' : 'Refresh retailers data'}
        </Button>
      </div>
      {hardRefreshError && <div className="text-red-500 text-sm mb-2">{hardRefreshError}</div>}
      <RetailerDialog onCreated={fetchRetailers} mode="create" />
      {loading ? (
        <p>Loading...</p>
      ) : (
        <div className="overflow-x-auto">
          <div className="max-h-[38rem] overflow-y-auto border rounded-xl shadow-lg bg-white dark:bg-gray-900">
            <table className="min-w-full text-sm border-separate border-spacing-0">
              <thead className="sticky top-0 z-10 bg-gray-800">
                <tr>
                  <th className="border-b border-r px-4 py-3 text-left text-white font-semibold w-1/3 cursor-pointer" onClick={() => { setSortBy('retailer_name'); setSortAsc(sortBy === 'retailer_name' ? !sortAsc : true); }}>
                    Name
                    <span className="ml-1">{sortBy === 'retailer_name' ? (sortAsc ? '▲' : '▼') : ''}</span>
                  </th>
                  <th className="border-b border-r px-4 py-3 text-left text-white font-semibold w-1/3 cursor-pointer" onClick={() => { setSortBy('retailer_location'); setSortAsc(sortBy === 'retailer_location' ? !sortAsc : true); }}>
                    Location
                    <span className="ml-1">{sortBy === 'retailer_location' ? (sortAsc ? '▲' : '▼') : ''}</span>
                  </th>
                  <th className="border-b border-r px-4 py-3 text-left text-white font-semibold w-1/3 cursor-pointer" onClick={() => { setSortBy('retailer_number'); setSortAsc(sortBy === 'retailer_number' ? !sortAsc : true); }}>
                    Number
                    <span className="ml-1">{sortBy === 'retailer_number' ? (sortAsc ? '▲' : '▼') : ''}</span>
                  </th>
                  <th className="border-b px-4 py-3 text-left text-white font-semibold w-24">Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedRetailers.map((r, idx) => (
                  <tr key={r.retailer_id} className={
                    `hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors` +
                    (idx !== paginatedRetailers.length - 1 ? ' border-b' : '')
                  }>
                    <td className="px-4 py-3 border-r align-top whitespace-nowrap">{r.retailer_name}</td>
                    <td className="px-4 py-3 border-r align-top whitespace-nowrap">{r.retailer_location ?? "-"}</td>
                    <td className="px-4 py-3 border-r align-top whitespace-nowrap">{r.retailer_number ?? "-"}</td>
                    <td className="px-4 py-3 align-top whitespace-nowrap flex gap-2">
                      <RetailerDialog
                        onCreated={fetchRetailers}
                        initial={r}
                        mode="edit"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="ml-2"
                        onClick={async () => {
                          if (confirm('Delete this retailer?')) {
                            const res = await deleteRetailer(r.retailer_id);
                            if (res.success) {
                              deleteRetailerFromStore(r.retailer_id);
                            }
                            fetchRetailers();
                          }
                        }}
                      >
                        Delete
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="flex justify-center items-center mt-4 space-x-2">
            <button
              onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 rounded bg-gray-200 disabled:opacity-50"
            >
              Prev
            </button>
            <span>Page {currentPage} of {totalPages}</span>
            <button
              onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 rounded bg-gray-200 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
