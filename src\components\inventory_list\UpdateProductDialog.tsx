import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { InventoryItem } from '@/database/schema';
import { useInventoryStore } from '@/lib/store/inventoryStore';
import { useToast } from '@/components/ui/toast';
import { updateProduct } from '@/lib/api/products';
import { publishInventoryUpdate } from '@/lib/realtime/publishInventoryUpdate';

interface UpdateProductDialogProps {
  product: InventoryItem;
  isOpen: boolean;
  onClose: () => void;
}

export const UpdateProductDialog: React.FC<UpdateProductDialogProps> = ({ product, isOpen, onClose }) => {
  const [form, setForm] = useState({ ...product });
  const [loading, setLoading] = useState(false);
  const { setInventoryItems, inventoryItems } = useInventoryStore();
  const { showToast } = useToast();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const updated = await updateProduct(form);
      setInventoryItems(inventoryItems.map(item => item.product_id === updated.product_id ? updated : item));
      publishInventoryUpdate([updated.product_id]);
      showToast('Product updated successfully', 'success');
      onClose();
    } catch {
      showToast('Failed to update product', 'error');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-900 p-6 rounded shadow-lg min-w-[320px]">
        <h2 className="text-lg font-bold mb-4">Update Product</h2>
        <div className="mb-2">
          <label className="block text-sm font-medium">Product Code</label>
          <input name="product_code" value={form.product_code} onChange={handleChange} className="w-full border rounded px-2 py-1" disabled />
        </div>
        <div className="mb-2">
          <label className="block text-sm font-medium">Quantity Left</label>
          <input name="quantity_left" type="number" value={form.quantity_left ?? ''} onChange={handleChange} className="w-full border rounded px-2 py-1" />
        </div>
        {/* Add more fields as needed */}
        <div className="flex justify-end space-x-2 mt-4">
          <Button type="button" variant="outline" onClick={onClose} disabled={loading}>Cancel</Button>
          <Button type="submit" disabled={loading}>Update</Button>
        </div>
      </form>
    </div>
  );
};
