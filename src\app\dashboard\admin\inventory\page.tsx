"use client";

import { InventoryDataTable } from "@/components/inventory_list/data-table";
import { AdvancedFilter } from "@/components/inventory_list/AdvancedFilter";
import { Input } from "@/components/ui/input";
import { useDebounce } from "@/lib/hooks/useDebounce";
import { useEffect, useState } from "react";
import { useInventoryStore } from "@/lib/store/inventoryStore";
import { Button } from '@/components/ui/button';
import { CreateProductDialog } from '@/components/inventory_list/CreateProductDialog';
import { ToastProvider, useToast } from '@/components/ui/toast';
import { useInventoryRealtimeSync } from '@/lib/realtime/useInventoryRealtimeSync';

/**
 * Reception Inventory Page component.
 * This page displays the inventory data table for receptionists.
 * @returns {JSX.Element} The rendered Reception Inventory page.
 */
export default function ReceptionInventoryPage() {
  return (
    <ToastProvider>
      <InventoryContent />
    </ToastProvider>
  );
}

/**
 * InventoryContent component to fetch and display inventory data.
 * @returns {JSX.Element} The rendered inventory content.
 */
function InventoryContent() {
  useInventoryRealtimeSync();
  const { isInitialLoading, error, getFilteredInventoryItems, applyFilters, advancedFilteredItems, searchTerm, setSearchTerm, currentPage, setCurrentPage, fetchInventory, inventoryItems } = useInventoryStore();
  const { showToast } = useToast();
  const [refreshing, setRefreshing] = useState(false);
  const pageSize = 100; // Display 100 items per page

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const filteredInventoryItems = getFilteredInventoryItems();
  const totalCount = advancedFilteredItems.length;

  useEffect(() => {
    applyFilters(); // Apply filters initially and on store changes
  }, [applyFilters]);

  useEffect(() => {
    // This useEffect will run when debouncedSearchTerm changes
    // The getFilteredInventoryItems will automatically react to searchTerm changes
    // No explicit action needed here as getFilteredInventoryItems is a selector
  }, [debouncedSearchTerm]);

  // React to changes in inventoryItems to ensure UI updates with real-time changes
  useEffect(() => {
    // This effect ensures the component re-renders when inventoryItems change
    // The applyFilters call in the store functions should handle updating advancedFilteredItems
  }, [inventoryItems]);

  const totalPages = Math.ceil(totalCount / pageSize);

  // Apply pagination to the filteredInventoryItems
  const paginatedInventoryItems = filteredInventoryItems.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  if (isInitialLoading && filteredInventoryItems.length === 0) return <div>Loading inventory...</div>;
  if (error) return <div>Error loading inventory: {error}</div>;

  const handleRefreshAll = async () => {
    setRefreshing(true);
    try {
      await fetchInventory(true);
      showToast('All products refreshed', 'success');
    } catch {
      showToast('Failed to refresh products', 'error');
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <>
      <div className="container mx-auto py-10">
        <h1 className="text-3xl font-bold mb-6">Reception Inventory Dashboard</h1>
        <div className="flex flex-col sm:flex-row justify-between items-center mb-4 space-y-2 sm:space-y-0 sm:space-x-4">
          <Input
            placeholder="Search product code..."
            value={searchTerm}
            onChange={(event) => setSearchTerm(event.target.value)}
            className="max-w-sm"
          />
          <div className="flex space-x-2 ml-auto">
            <Button variant="outline" onClick={handleRefreshAll} disabled={refreshing}>
              {refreshing ? 'Refreshing...' : 'Refresh All Products'}
            </Button>
            <CreateProductDialog />
          </div>
        </div>
          <AdvancedFilter />
        <InventoryDataTable data={paginatedInventoryItems} totalItemCount={totalCount}/>
      </div>
      <div className="fixed bottom-0 left-0 w-[calc(100%-16rem)] ml-64 bg-white dark:bg-gray-800 p-4 border-t shadow-lg flex justify-between items-center">
        <div className="flex space-x-2 mx-auto">
          <button
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-4 py-2 rounded-md bg-gray-200 dark:bg-gray-700 dark:text-gray-200 disabled:opacity-50"
          >
            &lt; Prev
          </button>
          {getPagination(currentPage, totalPages).map((page, index) => (
            <button
              key={index}
              onClick={() => typeof page === 'number' && setCurrentPage(page)}
              className={`px-4 py-2 rounded-md ${currentPage === page ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700 dark:text-gray-200'} ${page === '...' ? 'cursor-default' : ''}`}
              disabled={page === '...'}
            >
              {page}
            </button>
          ))}
          <button
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-4 py-2 rounded-md bg-gray-200 dark:bg-gray-700 dark:text-gray-200 disabled:opacity-50"
          >
            Next &gt;
          </button>
        </div>
        <div></div>
      </div>
    </>
  );
}

/**
 * Generates a smart pagination range with ellipses.
 * @param {number} currentPage - The current page number (1-indexed).
 * @param {number} totalPages - The total number of pages.
 * @returns {(number | string)[]} An array of page numbers and '...' for ellipses.
 */
function getPagination(currentPage: number, totalPages: number): (number | string)[] {
  const delta = 1;
  const range: number[] = [];
  const rangeWithDots: (number | string)[] = [];
  let l: number | undefined;

  for (let i = 1; i <= totalPages; i++) {
    if (
      i === 1 ||
      i === totalPages ||
      (i >= currentPage - delta && i <= currentPage + delta)
    ) {
      range.push(i);
    }
  }

  for (const i of range) {
    if (l) {
      if (i - l === 2) {
        rangeWithDots.push(l + 1);
      } else if (i - l !== 1) {
        rangeWithDots.push('...');
      }
    }
    rangeWithDots.push(i);
    l = i;
  }

  return rangeWithDots;
}