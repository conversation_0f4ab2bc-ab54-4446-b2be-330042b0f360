import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const { user_id, full_name, staff_role, phone_number, address } = req.body;
  if (!user_id || !full_name || !staff_role || !phone_number || !address) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  // Use service role key for update
  const { data, error } = await supabase
    .from('staff_members')
    .update({ full_name, staff_role, phone_number, address })
    .eq('user_id', user_id)
    .select();
  if (error) return res.status(400).json({ error: error.message });
  if (!data || data.length === 0) {
    return res.status(400).json({ error: 'No staff member updated. Check user_id.' });
  }
  return res.status(200).json({ success: true, updated: data[0] });
}
