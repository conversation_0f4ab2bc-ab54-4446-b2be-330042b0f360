"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, <PERSON>alogT<PERSON>le, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/Combobox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { productCategories, powerTypes, lensSides } from '@/lib/data/product_constants';
import { createProduct } from '@/lib/api/products';
import { useInventoryStore } from '@/lib/store/inventoryStore';
import { publishInventoryUpdate } from '@/lib/realtime/publishInventoryUpdate';




/**
 * Dialog component for creating a new product.
 * Allows input for basic product details and lens-specific parameters.
 * @returns {JSX.Element} The rendered dialog for new product creation.
 */
export function CreateProductDialog() {
  // Generate a random 10-digit number for product code
  function generateRandomProductCode() {
    return Math.floor(1000000000 + Math.random() * 9000000000);
  }
  const [isOpen, setIsOpen] = useState(false);
  const [productCode, setProductCode] = useState<number>(generateRandomProductCode());
  const [category, setCategory] = useState<string | null>(null);
  const [powerType, setPowerType] = useState<string>('Plano');
  const [sphericalValue, setSphericalValue] = useState<number | null>(null);
  const [cylindricalValue, setCylindricalValue] = useState<number | null>(null);
  const [axisValue, setAxisValue] = useState<number | null>(null);
  const [addValue, setAddValue] = useState<number | null>(null);
  const [lensSide, setLensSide] = useState<'Left' | 'Right' | null>(null);
  const [restockThreshold, setRestockThreshold] = useState<number>(0);
  const [initialQuantity, setInitialQuantity] = useState<number>(0);

  /**
   * Handles the submission of the new product form.
   * Currently logs the collected data to the console.
   */
  const handleSubmit = async () => {
    try {
      const result = await createProduct({
        product_code: productCode,
        product_category: category,
        power_type: powerType,
        price_per_unit: 0, // Adding required price_per_unit field with default value
        spherical_value: sphericalValue,
        cylindrical_value: cylindricalValue,
        axis_value: axisValue,
        add_value: addValue,
        lens_side: lensSide,
        restock_threshold: restockThreshold,
        quantity_initial: initialQuantity,
        quantity_added: 0,
        quantity_total: initialQuantity,
        quantity_out: 0,
        quantity_left: initialQuantity,
        updated_at: new Date().toISOString(),
        quantity_reserved: 0, // Fix: add required field
        });
      
      if (result) {
          alert('Product created successfully!');
          setIsOpen(false);
          resetForm();

          // Add the new product to the inventory store and IndexedDB
          useInventoryStore.getState().addInventoryItem({
            product_id: result.product_id,
            price_per_unit: 0, // Adding required price_per_unit field
            product_code: productCode,
            product_category: category,
            power_type: powerType,
            spherical_value: sphericalValue,
            cylindrical_value: cylindricalValue,
            axis_value: axisValue,
            add_value: addValue,
            lens_side: lensSide,
            restock_threshold: restockThreshold,
            quantity_initial: initialQuantity,
            quantity_added: 0,
            quantity_total: initialQuantity,
            quantity_out: 0,
            quantity_left: initialQuantity,
            entry_date: result.entry_date, // Use the entry_date returned from the database
            updated_at: new Date().toISOString(),
            quantity_reserved: 0, // Fix: add required field
          });
        publishInventoryUpdate([String(result.product_id)]); // Ensure product_id is string for Ably event
        console.log('[Ably] Product create event published for:', result.product_id);
        // Also update IndexedDB for other clients to pick up
        // (no change needed here, Ably event will trigger fetchProductsByIds in other clients)
        useInventoryStore.getState().applyFilters(); // Apply filters to update the displayed list
      }
      
    } catch (error) {
      console.error('Error creating product:', error);
      alert('Failed to create product. Please try again.');
    }
  };

  /**
   * Resets all form fields to their initial state.
   */
  const resetForm = () => {
    setProductCode(0);
    setCategory(null);
    setPowerType('Plano');
    setSphericalValue(null);
    setCylindricalValue(null);
    setAxisValue(null);
    setAddValue(null);
    setLensSide(null);
    setRestockThreshold(0);
    setInitialQuantity(0);
  };

  // When dialog opens, always generate a new random product code
  useEffect(() => {
    if (isOpen) {
      setProductCode(generateRandomProductCode());
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open);
      if (!open) {
        resetForm(); // Reset form when dialog is closed
      }
    }}>
      <DialogTrigger asChild>
        <Button className="ml-4">Add New Product</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Product</DialogTitle>
          <DialogDescription>
            Enter the details for the new product. Click save when you&apos;re done.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Basic Product Information */}
          <h3 className="text-lg font-semibold">Basic Product Information</h3>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="productCode" className="text-right">Product Code</Label>
            <Input
              id="productCode"
              type="text"
              value={String(productCode)}
              readOnly
              className="col-span-3 text-muted-foreground bg-gray-100 cursor-not-allowed"
              placeholder={String(productCode)}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category" className="text-right">Category</Label>
            <div className="col-span-3">
              <Combobox
                items={productCategories}
                value={category}
                onValueChange={(value) => setCategory(value || null)}
                placeholder="Select Category"
                displayKey="product_category"
                searchPlaceholder="Search category..."
              />
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="powerType" className="text-right">Power Type</Label>
            <div className="col-span-3">
              <Combobox
                items={powerTypes}
                value={powerType}
                onValueChange={(value) => setPowerType(value || 'Plano')}
                placeholder="Select Power Type"
                displayKey="power_type"
                searchPlaceholder="Search power type..."
              />
            </div>
          </div>

          {/* Lens Parameters */}
          <h3 className="text-lg font-semibold mt-4">Lens Parameters</h3>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sphericalValue" className="text-right">SPH</Label>
            <Input
              id="sphericalValue"
              type="number"
              step="0.25"
              value={sphericalValue === null ? '' : sphericalValue.toString()}
              onChange={(e) => {
                const value = e.target.value;
                setSphericalValue(value === '' ? null : parseFloat(value));
              }}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="cylindricalValue" className="text-right">CYL</Label>
            <Input
              id="cylindricalValue"
              type="number"
              step="0.25"
              value={cylindricalValue === null ? '' : cylindricalValue.toString()}
              onChange={(e) => {
                const value = e.target.value;
                setCylindricalValue(value === '' ? null : parseFloat(value));
              }}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="axisValue" className="text-right">AXIS</Label>
            <Input
              id="axisValue"
              type="number"
              step="90"
              value={axisValue === null ? '' : axisValue.toString()}
              onChange={(e) => {
                const value = e.target.value;
                setAxisValue(value === '' ? null : parseFloat(value));
              }}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="addValue" className="text-right">ADD</Label>
            <Input
              id="addValue"
              type="number"
              step="0.25"
              value={addValue === null ? '' : addValue.toString()}
              onChange={(e) => {
                const value = e.target.value;
                setAddValue(value === '' ? null : parseFloat(value));
              }}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="lensSide" className="text-right">Lens Side</Label>
            <div className="col-span-3">
              <Select
              onValueChange={(value) => setLensSide(value === "null" ? null : value as 'Left' | 'Right')}
              value={lensSide === null ? "null" : lensSide}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Lens Side" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="null">None</SelectItem>
                  {lensSides.map((side) => (
                    <SelectItem key={side} value={side}>
                      {side}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Inventory Management */}
          <h3 className="text-lg font-semibold mt-4">Inventory Management</h3>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="restockThreshold" className="text-right">Restock Threshold</Label>
            <Input
              id="restockThreshold"
              type="text"
              value={restockThreshold === null ? '' : String(restockThreshold)}
              onChange={(e) => {
                const val = e.target.value.replace(/[^0-9]/g, '');
                setRestockThreshold(val === '' ? 0 : parseInt(val));
              }}
              className="w-24 text-right"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="0"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="initialQuantity" className="text-right">Initial Quantity</Label>
            <Input
              id="initialQuantity"
              type="text"
              value={initialQuantity === null ? '' : String(initialQuantity)}
              onChange={(e) => {
                const val = e.target.value.replace(/[^0-9]/g, '');
                setInitialQuantity(val === '' ? 0 : parseInt(val));
              }}
              className="w-24 text-right"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="0"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>Cancel</Button>
          <Button onClick={handleSubmit}>Add Product</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}