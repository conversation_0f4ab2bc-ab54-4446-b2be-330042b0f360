import { ColumnDef } from "@tanstack/react-table";
import { Order } from "@/types/order";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { AlertDialog, AlertDialogContent, AlertDialogTitle, AlertDialogDescription, AlertDialogAction, AlertDialogCancel } from "@/components/ui/alert-dialog";
import { deleteOrder } from "@/lib/api/orders";
import { fetchStaffMembers } from '@/lib/api/admin';
import { useAuthStore } from '@/lib/store/authStore';
import { fetchOrderBillItems } from '@/lib/api/fetchOrderBillItems';
import { confirmOrderAndReserveStock } from '@/lib/api/confirmOrderAndReserveStock';
import { completeOrder } from '@/lib/api/completeOrder';
import React, { useState } from "react";

const OrderActions = ({ order, onView, onOrderChanged }: { order: Order, onView: (order: Order) => void, onOrderChanged?: () => void }) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showCompleteDialog, setShowCompleteDialog] = useState(false);
  const [deliveryStaff, setDeliveryStaff] = useState<{ user_id: string; full_name: string }[]>([]);
  const [selectedDelivery, setSelectedDelivery] = useState<string>("");
  const { user, fullName } = useAuthStore() as { user: { id: string } | null, fullName: string };

  React.useEffect(() => {
    if (showConfirmDialog) {
      fetchStaffMembers().then(staffs => {
        setDeliveryStaff(
          staffs
            .filter(s => s.staff_role === 'delivery' && s.user_id && s.full_name)
            .map(s => ({ user_id: s.user_id, full_name: s.full_name || '' }))
        );
      });
    }
  }, [showConfirmDialog]);

  const handleAction = (action: string) => {
    if (action === "View" || action === "View Detail") {
      onView(order);
    } else if (action === "Delete") {
      setShowDeleteDialog(true);
    } else if (action === "Confirm") {
      setShowConfirmDialog(true);
    } else if (action === "Complete") {
      setShowCompleteDialog(true);
    } else {
      console.log(`${action} actions for order id: ${order.order_id}`);
    }
  };

  const handleDelete = async () => {
    const success = await deleteOrder(order.order_id);
    if (success) {
      alert("Order deleted successfully.");
      if (onOrderChanged) onOrderChanged();
    } else {
      alert("Failed to delete order.");
    }
    setShowDeleteDialog(false);
  };

  const handleConfirm = async () => {
    const delivery = deliveryStaff.find(s => s.user_id === selectedDelivery);
    const billItems = await fetchOrderBillItems(order.order_id);
    // Prepare order update fields
    if (!user?.id) {
      alert('No user found for dispatching the order.');
      setShowConfirmDialog(false);
      return;
    }
    const orderUpdate = {
      order_id: order.order_id,
      order_dispatched_by: user.id,
      dispatched_by_display_name: fullName ?? null,
      delivered_by: delivery?.user_id,
      delivered_by_display_name: delivery?.full_name,
      dispatched_at: new Date().toISOString(),
    };
    // Log the data being sent for confirmation
    console.log('Order confirmation data:', { orderUpdate, billItems });
    // Call backend to update order and reserve stock
    const success = await confirmOrderAndReserveStock(orderUpdate, billItems);
    if (success) {
      alert('Order confirmed and stock reserved.');
      if (onOrderChanged) onOrderChanged();
    } else {
      alert('Failed to confirm order.');
    }
    setShowConfirmDialog(false);
  };

  const handleComplete = async () => {
    // Fetch order bill items before calling the API
    const billItems = await fetchOrderBillItems(order.order_id);
    // Prepare data for order complete API
    const completeOrderData = {
      order_id: order.order_id,
      completed_at: new Date().toISOString(),
      products: billItems.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity
      }))
    };
    // Log the data being sent for completion
    console.log('Order complete data:', completeOrderData);
    // Call the backend API to complete the order using the shared function
    const result = await import('@/lib/api/completeOrderAndUpdateStock').then(mod => mod.completeOrderAndUpdateStock(order.order_id, billItems));
    if (!result.success) {
      alert('Failed to complete order: ' + (result.error || 'Unknown error'));
    } else {
      alert('Order marked as complete!');
      if (onOrderChanged) onOrderChanged();
    }
    setShowCompleteDialog(false);
  };

  let actions: { label: string; onClick: () => void }[] = [];
  switch (order.order_status) {
    case "pending":
      actions = [
        { label: "View", onClick: () => handleAction("View") },
        { label: "Confirm", onClick: () => handleAction("Confirm") },
        { label: "Delete", onClick: () => handleAction("Delete") },
      ];
      break;
    case "active":
      actions = [
        { label: "Complete", onClick: () => handleAction("Complete") },
        { label: "Cancel", onClick: () => handleAction("Cancel") },
        { label: "View", onClick: () => handleAction("View") },
        
      ];
      break;
    case "completed":
      actions = [
        { label: "View Detail", onClick: () => handleAction("View Detail") },
      ];
      break;
    case "cancelled":
      actions = [
        { label: "View Detail", onClick: () => handleAction("View Detail") },
      ];
      break;
    default:
      actions = [];
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          {actions.map((action) => (
            <DropdownMenuItem key={action.label} onClick={action.onClick}>
              {action.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogTitle>Delete Order</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this order? This will also delete all related order bill items. This action cannot be undone.
          </AlertDialogDescription>
          <div className="flex justify-end gap-2 mt-4">
            <AlertDialogCancel asChild>
              <Button variant="outline">Cancel</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button variant="destructive" onClick={handleDelete}>Delete</Button>
            </AlertDialogAction>
          </div>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogTitle>Confirm Order</AlertDialogTitle>
          <AlertDialogDescription>
            <span className="mb-2 block">Select delivery staff before confirming. This will set the order status to active and update related fields.</span>
            <select
              className="w-full border rounded px-2 py-2 mb-2"
              value={selectedDelivery}
              onChange={e => setSelectedDelivery(e.target.value)}
              required
            >
              <option value="">Select Delivery Staff</option>
              {deliveryStaff.map(s => (
                <option key={s.user_id} value={s.user_id}>{s.full_name}</option>
              ))}
            </select>
          </AlertDialogDescription>
          <div className="flex justify-end gap-2 mt-4">
            <AlertDialogCancel asChild>
              <Button variant="outline">Cancel</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button variant="default" onClick={handleConfirm} disabled={!selectedDelivery}>Confirm</Button>
            </AlertDialogAction>
          </div>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={showCompleteDialog} onOpenChange={setShowCompleteDialog}>
        <AlertDialogContent>
          <AlertDialogTitle>Complete Order</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to mark this order as <b>complete</b>? This will update the order status and product stock accordingly.
          </AlertDialogDescription>
          <div className="flex justify-end gap-2 mt-4">
            <AlertDialogCancel asChild>
              <Button variant="outline">Cancel</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button variant="default" onClick={handleComplete}>Complete</Button>
            </AlertDialogAction>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export function withActionsColumn(columns: ColumnDef<Order>[], onView: (order: Order) => void, onOrderChanged?: () => void): ColumnDef<Order>[] {
  return [
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => <OrderActions order={row.original} onView={onView} onOrderChanged={onOrderChanged} />,
    },
    ...columns,
  ];
}