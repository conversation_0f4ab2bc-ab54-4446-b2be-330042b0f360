import axios from 'axios';

export async function createStaffMember({
  email,
  password,
  full_name,
  staff_role,
  phone_number,
  address,
}: {
  email: string;
  password: string;
  full_name: string;
  staff_role: string;
  phone_number: string;
  address: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const res = await axios.post('/api/staff/create', {
      email,
      password,
      full_name,
      staff_role,
      phone_number,
      address,
    });
    return res.data;
  } catch (err: unknown) {
    return { success: false, error: isAxiosError(err) ? err.response?.data?.error : (err as Error).message };
  }
}

export async function updateStaffMember(user_id: string, data: {
  full_name: string;
  staff_role: string;
  phone_number: string;
  address: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const res = await axios.post('/api/staff/update', {
      user_id,
      ...data,
    });
    return res.data;
  } catch (err: unknown) {
    return { success: false, error: isAxiosError(err) ? err.response?.data?.error : (err as Error).message };
  }
}

export async function deleteStaffMember(user_id: string): Promise<{ success: boolean; error?: string }> {
  try {
    const res = await axios.post('/api/staff/delete', { user_id });
    return res.data;
  } catch (err: unknown) {
    return { success: false, error: isAxiosError(err) ? err.response?.data?.error : (err as Error).message };
  }
}

function isAxiosError(error: unknown): error is { response?: { data?: { error?: string } } } {
  return (error as { response?: unknown }).response !== undefined;
}
