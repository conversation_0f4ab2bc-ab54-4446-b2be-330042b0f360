'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { signIn, getStaffRole, getStaffFullName } from '@/lib/api/auth';
import { useAuthStore } from '@/lib/store/authStore';
import { useInventoryStore } from '@/lib/store/inventoryStore';
import { useRetailerStore } from '@/lib/store/retailerStore';
import { useStaffStore } from '@/lib/store/staffStore';
import { useState } from 'react';

/**
 * Defines the schema for the login form using Zod.
 * @typedef {object} LoginFormSchema
 * @property {string} email - The user's email address.
 * @property {string} password - The user's password.
 * @property {'admin' | 'reception' | 'finance'} role - The user's role.
 */
const formSchema = z.object({
  email: z.string().email({ message: 'Invalid email address.' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters.' }),
});

type LoginFormValues = z.infer<typeof formSchema>;

/**
 * Renders the login page component.
 * @returns {JSX.Element} The login page.
 */
export default function LoginPage() {
  const router = useRouter();
  const { setUser, setRole, setFullName } = useAuthStore();
  const { fetchInventory } = useInventoryStore();
  const { fetchRetailers } = useRetailerStore();
  const { fetchStaff } = useStaffStore();
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  /**
   * Handles the form submission.
   * @param {LoginFormValues} values - The form data.
   */
  const onSubmit = async (values: LoginFormValues) => {
    setError(null);
    const { email, password } = values;

    try {
      const { user } = await signIn(email, password);

      if (user) {
        const staffRole = await getStaffRole(user.id);
        const staffFullName = await getStaffFullName(user.id);

        if (staffRole) {
          console.log('User logged in:', user);
          console.log('User role:', staffRole);

          // Set user, role, and full name in Zustand store
          setUser({
            id: user.id,
            email: user.email,
            // Add other fields as required by your local User type
            // For example: name: staffFullName, etc.
          });
          setRole(staffRole);
          setFullName(staffFullName);

          // Fetch initial data in background
          fetchInventory();
          fetchRetailers();
          fetchStaff();

          // Redirect based on role
          switch (staffRole) {
            case 'admin':
              router.push('/dashboard/admin');
              break;
            case 'reception':
              router.push('/dashboard/reception');
              break;
            case 'finance':
              router.push('/dashboard/finance');
              break;
            default:
              console.warn('Unknown role, redirecting to default dashboard:', staffRole);
              router.push('/dashboard'); // Fallback for unknown roles
              break;
          }
        } else {
          console.error('No staff record found for user:', user.id);
          setError('No staff record found for this user.');
        }
      }
    } catch (error: unknown) {
      console.error('Login error:', (error as Error).message);
      setError((error as Error).message || 'An unexpected error occurred.');
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-md">
        <h2 className="mb-6 text-center text-2xl font-bold">Login</h2>
        {error && (
          <div className="mb-4 rounded-md bg-red-100 p-3 text-sm text-red-700">
            {error}
          </div>
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="******" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              Login
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}