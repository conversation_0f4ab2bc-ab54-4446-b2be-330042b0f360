import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Order } from '@/types/order';
import { fetchOrdersPaginated } from '@/lib/api/orders';

interface OrdersState {
  // Data
  allOrders: Order[];
  pendingOrders: Order[];
  activeOrders: Order[];
  completedOrders: Order[];
  cancelledOrders: Order[];
  
  // Pagination & Counts
  totalCounts: {
    all: number;
    pending: number;
    active: number;
    completed: number;
    cancelled: number;
  };
  
  currentPage: number;
  pageSize: number;
  
  // Loading states
  isInitialLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  lastFetched: number | null;
  
  // Actions
  fetchInitialOrders: () => Promise<void>;
  loadMoreOrders: (status: string) => Promise<void>;
  setCurrentPage: (page: number) => void;
  refreshOrders: () => Promise<void>;
  addOrder: (order: Order) => void;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  removeOrder: (orderId: string) => void;
}

export const useOrdersStore = create<OrdersState>()(
  persist(
    (set, get) => ({
      // Initial state
      allOrders: [],
      pendingOrders: [],
      activeOrders: [],
      completedOrders: [],
      cancelledOrders: [],
      
      totalCounts: {
        all: 0,
        pending: 0,
        active: 0,
        completed: 0,
        cancelled: 0,
      },
      
      currentPage: 1,
      pageSize: 50, // 50 orders per page as requested
      
      isInitialLoading: false,
      isLoadingMore: false,
      error: null,
      lastFetched: null,
      
      // Actions
      fetchInitialOrders: async () => {
        const now = Date.now();
        const { lastFetched } = get();
        
        // Only fetch if we haven't fetched in the last 5 minutes or if we have no data
        if (lastFetched && (now - lastFetched < 5 * 60 * 1000) && get().allOrders.length > 0) {
          return;
        }
        
        set({ isInitialLoading: true, error: null });
        
        try {
          // Fetch initial 100 orders and total counts
          const [
            allOrdersData,
            pendingOrdersData,
            activeOrdersData,
            completedOrdersData,
            cancelledOrdersData,
            allCount,
            pendingCount,
            activeCount,
            completedCount,
            cancelledCount
          ] = await Promise.all([
            fetchOrdersPaginated(0, 100, 'all') as Promise<Order[]>,
            fetchOrdersPaginated(0, 100, 'pending') as Promise<Order[]>,
            fetchOrdersPaginated(0, 100, 'active') as Promise<Order[]>,
            fetchOrdersPaginated(0, 100, 'completed') as Promise<Order[]>,
            fetchOrdersPaginated(0, 100, 'cancelled') as Promise<Order[]>,
            fetchOrdersPaginated(0, 0, 'all', true) as Promise<number>,
            fetchOrdersPaginated(0, 0, 'pending', true) as Promise<number>,
            fetchOrdersPaginated(0, 0, 'active', true) as Promise<number>,
            fetchOrdersPaginated(0, 0, 'completed', true) as Promise<number>,
            fetchOrdersPaginated(0, 0, 'cancelled', true) as Promise<number>,
          ]);
          
          set({
            allOrders: allOrdersData,
            pendingOrders: pendingOrdersData,
            activeOrders: activeOrdersData,
            completedOrders: completedOrdersData,
            cancelledOrders: cancelledOrdersData,
            totalCounts: {
              all: allCount,
              pending: pendingCount,
              active: activeCount,
              completed: completedCount,
              cancelled: cancelledCount,
            },
            lastFetched: now,
            isInitialLoading: false,
          });
          
          console.log('Orders fetched successfully:', {
            all: allOrdersData.length,
            pending: pendingOrdersData.length,
            active: activeOrdersData.length,
            completed: completedOrdersData.length,
            cancelled: cancelledOrdersData.length,
          });
          
        } catch (error) {
          console.error('Error fetching orders:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch orders',
            isInitialLoading: false 
          });
        }
      },
      
      loadMoreOrders: async (status: string) => {
        const { isLoadingMore } = get();
        if (isLoadingMore) return;
        
        set({ isLoadingMore: true });
        
        try {
          const currentOrders = get()[`${status}Orders` as keyof OrdersState] as Order[];
          const offset = currentOrders.length;
          const limit = get().pageSize;
          
          const moreOrders = await fetchOrdersPaginated(offset, limit, status) as Order[];
          
          set((state) => ({
            [`${status}Orders`]: [...(state[`${status}Orders` as keyof OrdersState] as Order[]), ...moreOrders],
            isLoadingMore: false,
          }));
          
        } catch (error) {
          console.error('Error loading more orders:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load more orders',
            isLoadingMore: false 
          });
        }
      },
      
      setCurrentPage: (page: number) => {
        set({ currentPage: page });
      },
      
      refreshOrders: async () => {
        set({ lastFetched: null });
        await get().fetchInitialOrders();
      },
      
      addOrder: (order: Order) => {
        set((state) => {
          const newAllOrders = [order, ...state.allOrders];
          const updates: Partial<OrdersState> = { allOrders: newAllOrders };
          
          // Add to appropriate status array
          if (order.order_status === 'pending') {
            updates.pendingOrders = [order, ...state.pendingOrders];
          } else if (['active', 'dispatched', 'waiting for delivery success'].includes(order.order_status)) {
            updates.activeOrders = [order, ...state.activeOrders];
          } else if (order.order_status === 'completed') {
            updates.completedOrders = [order, ...state.completedOrders];
          } else if (order.order_status === 'cancelled') {
            updates.cancelledOrders = [order, ...state.cancelledOrders];
          }
          
          return updates;
        });
      },
      
      updateOrder: (orderId: string, updates: Partial<Order>) => {
        set((state) => {
          const updateOrderInArray = (orders: Order[]) =>
            orders.map(order => order.order_id === orderId ? { ...order, ...updates } : order);
          
          return {
            allOrders: updateOrderInArray(state.allOrders),
            pendingOrders: updateOrderInArray(state.pendingOrders),
            activeOrders: updateOrderInArray(state.activeOrders),
            completedOrders: updateOrderInArray(state.completedOrders),
            cancelledOrders: updateOrderInArray(state.cancelledOrders),
          };
        });
      },
      
      removeOrder: (orderId: string) => {
        set((state) => {
          const removeFromArray = (orders: Order[]) =>
            orders.filter(order => order.order_id !== orderId);
          
          return {
            allOrders: removeFromArray(state.allOrders),
            pendingOrders: removeFromArray(state.pendingOrders),
            activeOrders: removeFromArray(state.activeOrders),
            completedOrders: removeFromArray(state.completedOrders),
            cancelledOrders: removeFromArray(state.cancelledOrders),
          };
        });
      },
    }),
    {
      name: 'orders-store',
      partialize: (state) => ({
        allOrders: state.allOrders,
        pendingOrders: state.pendingOrders,
        activeOrders: state.activeOrders,
        completedOrders: state.completedOrders,
        cancelledOrders: state.cancelledOrders,
        totalCounts: state.totalCounts,
        lastFetched: state.lastFetched,
      }),
    }
  )
);
