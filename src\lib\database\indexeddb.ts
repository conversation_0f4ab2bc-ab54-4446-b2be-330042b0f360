import Dexie, { Table } from 'dexie';
import { InventoryItem } from '@/database/schema';

export class MySubClassedDexie extends Dexie {
  inventoryItems!: Table<InventoryItem>;

  constructor() {
    super('hisabkitabDatabase');
    this.version(1).stores({
      inventoryItems: 'product_id, product_code, product_category, power_type, quantity_left, spherical_value, cylindrical_value, add_value, axis_value, lens_side',
    });
  }
}

export const db = new MySubClassedDexie();