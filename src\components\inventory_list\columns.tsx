"use client";

import { ColumnDef } from "@tanstack/react-table";
import { SelectionActionCell } from './SelectionActionCell';
import { UpdateActionCell } from './UpdateActionCell';
import React from "react";

import { InventoryItem } from "@/database/schema";

/**
 * Defines the columns for the inventory data table.
 * @type {ColumnDef<InventoryItem>[]} 
 */

/**
 * Defines the columns for the inventory data table.
 * @type {ColumnDef<InventoryItem>[]}
 */
export function getInventoryColumns(selectedProducts: InventoryItem[], setSelectedProducts: (items: InventoryItem[]) => void): ColumnDef<InventoryItem>[] {
  return [
    {
      id: "selectionAction",
      header: "Add to List",
      size: 50, // Fixed column width to fit the icon and header
      cell: ({ row }) => {
        const inventoryItem = row.original as InventoryItem;
        return (
          <SelectionActionCell
            inventoryItem={inventoryItem}
            selectedProducts={selectedProducts}
            setSelectedProducts={setSelectedProducts}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "product_code",
      header: "Product Code",
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("product_code")}
        </div>
      ),
    },
    {
      accessorKey: "product_category",
      header: "Category",
      enableSorting: false,
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("product_category")}
        </div>
      ),
    },
    {
      accessorKey: "power_type",
      header: "Power Type",
      enableSorting: false,
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("power_type")}
        </div>
      ),
    },
    {
      accessorKey: "spherical_value",
      header: "SPH",
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("spherical_value") !== null ? (Number(row.getValue("spherical_value")) >= 0 ? '+' : '') + Number(row.getValue("spherical_value")).toFixed(2) : ''}
        </div>
      ),
    },
    {
      accessorKey: "cylindrical_value",
      header: "CYL",
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("cylindrical_value") !== null ? (Number(row.getValue("cylindrical_value")) >= 0 ? '+' : '') + Number(row.getValue("cylindrical_value")).toFixed(2) : ''}
        </div>
      ),
    },
    {
      accessorKey: "axis_value",
      header: "AXIS",
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("axis_value") !== null ? (Number(row.getValue("axis_value")) >= 0 ? '+' : '') + Number(row.getValue("axis_value")).toFixed(2) : ''}
        </div>
      ),
    },
    {
      accessorKey: "add_value",
      header: "ADD",
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("add_value") !== null ? (Number(row.getValue("add_value")) >= 0 ? '+' : '') + Number(row.getValue("add_value")).toFixed(2) : ''}
        </div>
      ),
    },
    {
      accessorKey: "lens_side",
      header: "Lens Side",
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("lens_side")}
        </div>
      ),
    },
    {
      accessorKey: "quantity_left",
      header: "Quantity Left",
      cell: ({ row }) => (
        <div className="px-2 py-1">
          {row.getValue("quantity_left")}
        </div>
      ),
    },
    {
      id: 'update',
      header: 'Update',
      size: 80,
      cell: ({ row }) => {
        const inventoryItem = row.original as InventoryItem;
        return <UpdateActionCell inventoryItem={inventoryItem} />;
      },
      enableSorting: false,
      enableHiding: false,
    },
  ];
}