"use client";

import * as React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/Combobox";
import { productCategories } from '@/lib/data/product_constants';
import { useInventoryStore } from '@/lib/store/inventoryStore';
import { InventoryItem } from '@/database/schema';

/**
 * @interface OrderItemSearchProps
 * @property {function({ items: InventoryItem[], totalCount: number }): void} onSearchResultsChange - Callback function to pass search results to the parent component.
 */
interface OrderItemSearchProps {
  onSearchResultsChange: (results: { items: InventoryItem[]; totalCount: number }) => void;
}

/**
 * Renders the product search section for order items.
 * @param {OrderItemSearchProps} props - The component props.
 * @returns {JSX.Element} The rendered product search section.
 */
export function OrderItemSearch({ onSearchResultsChange }: OrderItemSearchProps) {
  const [productCategory, setProductCategory] = React.useState<string | null>(null);
  const [productCode, setProductCode] = React.useState<string | null>(null);
  const [productCodeError, setProductCodeError] = React.useState<string | null>(null);
  const [sphValue, setSphValue] = React.useState<number | null>(null);
  const [cylValue, setCylValue] = React.useState<number | null>(null);
  const [addValue, setAddValue] = React.useState<number | null>(null);
  const [axisValue, setAxisValue] = React.useState<number | null>(null);
  const { inventoryItems } = useInventoryStore();

  // Debug log to track inventory changes
  React.useEffect(() => {
    console.log('[OrderItemSearch] inventoryItems updated, length:', inventoryItems.length);
  }, [inventoryItems]);

  /**
   * Resets all search filter input fields and clears search results.
   */
  const handleClearFilters = () => {
    setProductCategory(null);
    setProductCode(null);
    setSphValue(null);
    setCylValue(null);
    setAddValue(null);
    setAxisValue(null);
  };

  React.useEffect(() => {
    if (productCode !== null && productCode.length > 0 && productCode.length < 5) {
      setProductCodeError("Product code must be at least 5 characters long.");
    } else {
      setProductCodeError(null);
    }
  }, [productCode]);

  // Manual search for button
  const handleManualSearch = React.useCallback(() => {
    console.log('[OrderItemSearch] handleManualSearch called, inventoryItems length:', inventoryItems.length);
    if (productCode !== null && productCode.length > 0 && productCode.length < 5) {
      setProductCodeError("Product code must be at least 5 characters long.");
      return;
    }
    let filteredResults = inventoryItems;
    if (productCategory) {
      filteredResults = filteredResults.filter(item => item.product_category?.toLowerCase() === productCategory.toLowerCase());
    }
    if (productCode && productCode.length >= 5) {
      filteredResults = filteredResults.filter(item => item.product_code.toString().includes(productCode));
    }
    if (sphValue !== null) {
      filteredResults = filteredResults.filter(item => item.spherical_value === sphValue);
    }
    if (cylValue !== null) {
      filteredResults = filteredResults.filter(item => item.cylindrical_value === cylValue);
    }
    if (addValue !== null) {
      filteredResults = filteredResults.filter(item => item.add_value === addValue);
    }
    if (axisValue !== null) {
      filteredResults = filteredResults.filter(item => item.axis_value === axisValue);
    }
    const resultCount = filteredResults.length;
    const resultsToReturn = resultCount > 5 ? filteredResults.slice(0, 5) : filteredResults;
    console.log('[OrderItemSearch] search effect triggered, filtered results:', resultCount);
    onSearchResultsChange({
      items: resultsToReturn,
      totalCount: resultCount
    });
  }, [productCategory, productCode, sphValue, cylValue, addValue, axisValue, inventoryItems, onSearchResultsChange]);

  return (
    <>
      <hr className="my-4" />
      <div className="flex items-center justify-between">
        <Label htmlFor="productSearch" className="text-left">
          Product Search
        </Label>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-7 gap-3 items-end">
        <div className="col-span-full flex items-center">
          <Input
            type="text"
            placeholder="Product Code"
            value={productCode === null ? '' : productCode}
            onChange={(e) => setProductCode(e.target.value === '' ? null : e.target.value)}
          />
        </div>
        {productCodeError && <p className="text-red-500 text-sm col-span-full">{productCodeError}</p>}

        <Combobox
          items={productCategories}
          value={productCategory}
          onValueChange={(value) => setProductCategory(value ? value.toUpperCase() : null)}
          placeholder="All Categories"
          displayKey="product_category"
          searchPlaceholder="Search category..."
        />

        <Input
          type="number"
          placeholder="SPH"
          step="0.25"
          value={sphValue === null ? '' : sphValue.toString()}
          onChange={(e) => {
            const value = e.target.value;
            setSphValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
          }}
          className="col-span-1"
        />

        <Input
          type="number"
          placeholder="CYL"
          step="0.25"
          value={cylValue === null ? '' : cylValue.toString()}
          onChange={(e) => {
            const value = e.target.value;
            setCylValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
          }}
          className="col-span-1"
        />

        <Input
          type="number"
          placeholder="ADD"
          step="0.25"
          value={addValue === null ? '' : addValue.toString()}
          onChange={(e) => {
            const value = e.target.value;
            setAddValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
          }}
          className="col-span-1"
        />

        <Input
          type="number"
          placeholder="AXIS"
          step="90"
          value={axisValue === null ? '' : axisValue.toString()}
          onChange={(e) => {
            const value = e.target.value;
            setAxisValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
          }}
          className="col-span-1"
        />
        <Button type="button" onClick={handleManualSearch} className="w-auto">Search</Button>
        <Button type="button" onClick={handleClearFilters} variant="outline" className="w-auto">Clear Filters</Button>
       
      </div>
    </>
  );
}