"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";

/**
 * @interface ComboboxProps
 * @template T The type of the items in the combobox list.
 * @property {T[]} items - An array of items to be displayed in the combobox.
 * @property {string | null} value - The currently selected value in the combobox.
 * @property {(value: string | null) => void} onValueChange - Callback function triggered when the selected value changes.
 * @property {string} placeholder - The placeholder text displayed when no item is selected.
 * @property {keyof T} displayKey - The key of the item object whose value should be displayed and used for searching.
 * @property {string} [searchPlaceholder] - Optional placeholder text for the search input within the combobox.
 * @property {string} [emptyMessage] - Optional message to display when no items match the search.
 */
interface ComboboxProps<T> {
  items: T[];
  value: string | null;
  onValueChange: (value: string | null) => void;
  placeholder: string;
  displayKey: keyof T;
  searchPlaceholder?: string;
  emptyMessage?: string;
}

/**
 * A generic combobox component for selecting items from a list.
 * It provides search functionality and accessibility features.
 * @template T The type of the items in the list.
 * @param {ComboboxProps<T>} props - The component props.
 * @returns {JSX.Element} The rendered combobox.
 */
export function Combobox<T>({ items, value, onValueChange, placeholder, displayKey, searchPlaceholder = "Search...", emptyMessage = "No item found." }: ComboboxProps<T>) {
  /**
   * State to control the open/close status of the combobox popover.
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */
  const [open, setOpen] = React.useState(false);
  /**
   * State to store the current value of the search input within the combobox.
   * @type {[string, React.Dispatch<React.SetStateAction<string>>]}
   */
  const [searchValue, setSearchValue] = React.useState("");

  const filteredItems = items.filter(item =>
    String(item[displayKey]).toLowerCase().includes(searchValue.toLowerCase())
  );

  const displayedValue = value ? items.find(item => String(item[displayKey]) === value)?.[displayKey] : placeholder;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[180px] justify-between"
        >
          {String(displayedValue)}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[180px] p-0">
        <Command>
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-48 overflow-y-auto">
                {filteredItems.map((item, index) => (
                  <CommandItem
                    key={index} // Using index as key, assuming items are unique or stable
                    value={String(item[displayKey])}
                    onSelect={() => {
                      onValueChange(String(item[displayKey]));
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === String(item[displayKey])
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {String(item[displayKey])}
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}