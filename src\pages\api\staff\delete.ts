import type { NextApiRequest, NextApiResponse } from 'next';
import { deleteStaffMember } from '@/lib/api/staffActions';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const { user_id } = req.body;
  if (!user_id) {
    return res.status(400).json({ error: 'Missing user_id' });
  }
  // Delete from staff_members table
  const result = await deleteStaffMember(user_id);
  if (!result.success) {
    return res.status(400).json({ error: result.error });
  }
  // Optionally, delete user from Supabase Auth
  const { error: authError } = await supabase.auth.admin.deleteUser(user_id);
  if (authError) {
    return res.status(400).json({ error: authError.message });
  }
  return res.status(200).json({ success: true });
}
