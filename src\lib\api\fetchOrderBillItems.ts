import { supabase } from '@/lib/supabaseClient';
import { OrderBillItem } from '@/types/order-bill-item';

export async function fetchOrderBillItems(orderId: string): Promise<OrderBillItem[]> {
  const { data, error } = await supabase
    .from('order_bill_items')
    .select('*')
    .eq('order_id', orderId);
  if (error) {
    console.error('Error fetching order bill items:', error.message);
    return [];
  }
  return data as OrderBillItem[];
}
