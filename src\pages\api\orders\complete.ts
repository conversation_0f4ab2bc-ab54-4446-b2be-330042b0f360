import type { NextApiRequest, NextApiResponse } from 'next';
import { completeOrderAndUpdateStock } from '@/lib/api/orders';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const { orderId } = req.body;
  if (!orderId) {
    return res.status(400).json({ error: 'Missing orderId' });
  }
  const success = await completeOrderAndUpdateStock(orderId);
  if (!success) {
    return res.status(500).json({ error: 'Failed to complete order' });
  }
  return res.status(200).json({ success: true });
}
