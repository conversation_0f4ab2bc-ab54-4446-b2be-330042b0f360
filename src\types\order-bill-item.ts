/**
 * @module types/order-bill-item
 * @description Defines the TypeScript types for order bill item-related data.
 */

/**
 * @typedef {Object} OrderBillItem
 * @property {string} id - Unique identifier for the order bill item.
 * @property {string} order_bill_id - The ID of the associated order bill.
 * @property {string} product_id - The ID of the product.
 * @property {number} quantity - The quantity of the product.
 * @property {number} price - The price of the product at the time of sale.
 */
export type OrderBillItem = {
  item_id: string;
  bill_id: string | null;
  product_id: string;
  product_description: string | null;
  quantity: number;
  price_per_unit: number;
  subtotal: number | null;
  order_id: string;
};