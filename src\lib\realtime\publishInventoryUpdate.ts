import ably from '@/lib/realtime/ablyClient';

/**
 * Publishes a real-time event to Ably after a product is created or updated.
 * @param productIds - Array of product IDs affected.
 */
export function publishInventoryUpdate(productIds: string[]) {
  if (!productIds || productIds.length === 0) return;
  const channel = ably.channels.get('inventory-updates');
  channel.publish('updated', { productIds });
}

/**
 * Publishes a real-time event to Ably after products are deleted.
 * @param productIds - Array of product IDs that were deleted.
 */
export function publishInventoryDelete(productIds: string[]) {
  if (!productIds || productIds.length === 0) return;
  const channel = ably.channels.get('inventory-updates');
  channel.publish('deleted', { productIds });
}
