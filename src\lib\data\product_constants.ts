/**
 * @file This file contains constant data for product categories and power types.
 */

/**
 * Array of product categories used for filtering and selection.
 * @type {Array<{ product_category: string }>}
 */
export const productCategories = [
  { "product_category": "ARC" },
  { "product_category": "ARC-KB" },
  { "product_category": "B-ARC" },
  { "product_category": "BCR-B-NC" },
  { "product_category": "BRC" },
  { "product_category": "BRC-B" },
  { "product_category": "BRC-KB" },
  { "product_category": "BRC-KB-PG" },
  { "product_category": "BRC-PG" },
  { "product_category": "HC" },
  { "product_category": "HC-KB" },
  { "product_category": "PG" },
  { "product_category": "PG-ARC" },
  { "product_category": "PG-ARC-KB" },
  { "product_category": "PG-KB" },
  { "product_category": "PG-KB-ARC" },
  { "product_category": "TINT" },
  { "product_category": "TRIO" }
];

/**
 * Array of power types used for filtering and selection.
 * @type {Array<{ power_type: string }>}
 */
export const powerTypes = [
  { "power_type": "Plano" },
  { "power_type": "Spherical" },
  { "power_type": "Cylindrical" },
  { "power_type": "Compound" },
];

/**
 * Array of lens sides used for selection.
 * @type {Array<string>}
 */
export const lensSides = ['Left', 'Right'];