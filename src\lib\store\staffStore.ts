import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { StaffMember } from '@/database/schema';
import { fetchStaffMembers } from '@/lib/api/admin';

interface StaffState {
  staff: StaffMember[];
  fetchStaff: (forceRefresh?: boolean) => Promise<void>;
  getStaffById: (id: string) => StaffMember | undefined;
  updateStaffInStore: (updated: StaffMember) => void;
  deleteStaffFromStore: (user_id: string) => void;
}

export const useStaffStore = create<StaffState>()(
  persist(
    (set, get) => ({
      staff: [],
      fetchStaff: async (forceRefresh = false) => {
        if (!forceRefresh && get().staff.length > 0) {
          // Data is already loaded from cache
          return;
        }
        const data = await fetchStaffMembers();
        set({ staff: data });
      },
      getStaffById: (id: string) => {
        return get().staff.find((s) => s.user_id === id);
      },
      updateStaffInStore: (updated: StaffMember) => {
        set((state) => ({
          staff: state.staff.map((s) =>
            s.user_id === updated.user_id ? { ...s, ...updated } : s
          ),
        }));
      },
      deleteStaffFromStore: (user_id: string) => {
        set((state) => ({
          staff: state.staff.filter((s) => s.user_id !== user_id),
        }));
      },
    }),
    {
      name: 'staff-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ staff: state.staff }),
    }
  )
);
