"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Combobox } from "@/components/ui/Combobox";
import { useInventoryStore } from "@/lib/store/inventoryStore";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { productCategories, powerTypes, lensSides } from '@/lib/data/product_constants';

/**
 * AdvancedFilter component for filtering inventory items by category and power type.
 * Includes a toggleable hide/show feature and an apply button.
 * @returns {JSX.Element} The rendered advanced filter section.
 */
export function AdvancedFilter() {
  const {
    selectedCategory,
    selectedPowerType,
    selectedSphericalValue,
    selectedCylindricalValue,
    selectedAddValue,
    selectedAxisValue,
    selectedLensSide,
    selectedProductCode,
    setSelectedCategory,
    setSelectedPowerType,
    setSelectedSphericalValue,
    setSelectedCylindricalValue,
    setSelectedAddValue,
    setSelectedAxisValue,
    setSelectedLensSide,
    setSelectedProductCode,
    applyFilters,
    clearFilters // Add clearFilters from the store
  } = useInventoryStore();

  /**
   * State to manage the expansion of the advanced filter section.
   * @type {boolean}
   */
  const [isExpanded, setIsExpanded] = useState(false);
  /**
   * Local state for the selected product category, synchronized with global store on apply.
   * @type {string | null}
   */
  const [localSelectedCategory, setLocalSelectedCategory] = useState(selectedCategory);
  /**
   * Local state for the selected power type, synchronized with global store on apply.
   * @type {string | null}
   */
  const [localSelectedPowerType, setLocalSelectedPowerType] = useState(selectedPowerType);
  /**
   * Local state for the selected spherical value, synchronized with global store on apply.
   * @type {number | null}
   */
  const [localSelectedSphericalValue, setLocalSelectedSphericalValue] = useState(selectedSphericalValue);
  /**
   * Local state for the selected cylindrical value, synchronized with global store on apply.
   * @type {number | null}
   */
  const [localSelectedCylindricalValue, setLocalSelectedCylindricalValue] = useState(selectedCylindricalValue);
  /**
   * Local state for the selected add value, synchronized with global store on apply.
   * @type {number | null}
   */
  const [localSelectedAddValue, setLocalSelectedAddValue] = useState(selectedAddValue);
  /**
   * Local state for the selected axis value, synchronized with global store on apply.
   * @type {number | null}
   */
  const [localSelectedAxisValue, setLocalSelectedAxisValue] = useState(selectedAxisValue);
  /**
   * Local state for the selected lens side, synchronized with global store on apply.
   * @type {'Left' | 'Right' | null}
   */
  const [localSelectedLensSide, setLocalSelectedLensSide] = useState(selectedLensSide);
  /**
   * Local state for the selected product code, synchronized with global store on apply.
   * @type {string | null}
   */
  const [localSelectedProductCode, setLocalSelectedProductCode] = useState(selectedProductCode);
  /**
   * State to indicate if the filters are currently being applied (for loading indicator).
   * @type {boolean}
   */
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setLocalSelectedCategory(selectedCategory);
    setLocalSelectedPowerType(selectedPowerType);
    setLocalSelectedSphericalValue(selectedSphericalValue);
    setLocalSelectedCylindricalValue(selectedCylindricalValue);
    setLocalSelectedAddValue(selectedAddValue);
    setLocalSelectedAxisValue(selectedAxisValue);
    setLocalSelectedLensSide(selectedLensSide ? (selectedLensSide.toLowerCase() as 'Left' | 'Right') : null);
  }, [selectedCategory, selectedPowerType, selectedSphericalValue, selectedCylindricalValue, selectedAddValue, selectedAxisValue, selectedLensSide, selectedProductCode]);

  /**
   * Handles the application of filters. Updates the global store with local filter states
   * and sets a loading indicator during the process.
   * @returns {Promise<void>}
   */
  const handleApplyFilters = async () => {
    setIsLoading(true);
    setSelectedCategory(localSelectedCategory);
    setSelectedPowerType(localSelectedPowerType);
    setSelectedSphericalValue(localSelectedSphericalValue);
    setSelectedCylindricalValue(localSelectedCylindricalValue);
    setSelectedAddValue(localSelectedAddValue);
    setSelectedAxisValue(localSelectedAxisValue);
    setSelectedLensSide(localSelectedLensSide);
    setSelectedProductCode(localSelectedProductCode);
    await applyFilters(); // Assuming applyFilters might be async in the future
    setIsLoading(false);
  };

  /**
   * Handles clearing all filters. Resets local states and triggers the global store's clear action.
   */
  const handleClearFilters = async () => {
    setIsLoading(true);
    setLocalSelectedCategory(null);
    setLocalSelectedPowerType(null);
    setLocalSelectedSphericalValue(null);
    setLocalSelectedCylindricalValue(null);
    setLocalSelectedAddValue(null);
    setLocalSelectedAxisValue(null);
    setLocalSelectedLensSide(null);
    setLocalSelectedProductCode(null);
    await clearFilters(); // Call the clearFilters action from the store
    setIsLoading(false);
  };

  return (
    <div className="flex flex-col gap-4 mb-6">
      <Button
        variant="outline"
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-fit"
      >
        {isExpanded ? <ChevronUpIcon className="h-4 w-4 mr-2" /> : <ChevronDownIcon className="h-4 w-4 mr-2" />}
        {isExpanded ? "Hide Advanced Filters" : "Show Advanced Filters"}
      </Button>

      {isExpanded && (
        <div className="flex flex-col md:flex-row gap-4">
          <Combobox
            items={productCategories}
            value={localSelectedCategory}
            onValueChange={(value) => setLocalSelectedCategory(value ? value.toUpperCase() : null)}
            placeholder="All Categories"
            displayKey="product_category"
            searchPlaceholder="Search category..."
          />

          <Input
            type="number"
            placeholder="SPH"
            step="0.25"
            value={localSelectedSphericalValue === null ? '' : localSelectedSphericalValue.toString()}
            onChange={(e) => {
              const value = e.target.value;
              setLocalSelectedSphericalValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
            }}
            className="w-[100px]"
          />

          <Input
            type="number"
            placeholder="CYL"
            step="0.25"
            value={localSelectedCylindricalValue === null ? '' : localSelectedCylindricalValue.toString()}
            onChange={(e) => {
              const value = e.target.value;
              setLocalSelectedCylindricalValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
            }}
            className="w-[100px]"
          />

          <Input
            type="number"
            placeholder="ADD"
            step="0.25"
            value={localSelectedAddValue === null ? '' : localSelectedAddValue.toString()}
            onChange={(e) => {
              const value = e.target.value;
              setLocalSelectedAddValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
            }}
            className="w-[100px]"
          />

          <Input
            type="number"
            placeholder="AXIS"
            step="90"
            value={localSelectedAxisValue === null ? '' : localSelectedAxisValue.toString()}
            onChange={(e) => {
              const value = e.target.value;
              setLocalSelectedAxisValue(value === '' || parseFloat(value) === 0 ? null : parseFloat(value));
            }}
            className="w-[100px]"
          />

          <Combobox
            items={powerTypes}
            value={localSelectedPowerType}
            onValueChange={(value) => setLocalSelectedPowerType(value || null)}
            placeholder="All Power Types"
            displayKey="power_type"
            searchPlaceholder="Search power type..."
          />
          
          <Select
            onValueChange={(value) => setLocalSelectedLensSide(value === "null" ? null : value as 'Left' | 'Right')}
            value={localSelectedLensSide === null ? "null" : localSelectedLensSide}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by Lens Side" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="null">none</SelectItem>
              {lensSides.map((side) => (
                <SelectItem key={side} value={side.toLowerCase()}>
                  {side}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button onClick={handleApplyFilters} disabled={isLoading}>
            {isLoading ? "Applying..." : "Apply Filters"}
          </Button>
          <Button onClick={handleClearFilters} disabled={isLoading} variant="outline">
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
}