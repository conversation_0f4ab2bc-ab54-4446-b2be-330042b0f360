/**
 * @module types/order
 * @description Defines the TypeScript types for order-related data.
 */

/**
 * @typedef {Object} Order
 * @property {string} id - Unique identifier for the order.
 * @property {string} customer_name - The name of the customer.
 * @property {number} total_amount - The total amount of the order.
 * @property {Date} created_at - The timestamp when the order was created.
 */
export type Order = {
  order_id: string;
  order_code: string;
  retailer_display_name: string | null;
  order_issued_by: string;
  issued_by_display_name: string | null;
  order_dispatched_by: string | null;
  dispatched_by_display_name: string | null;
  order_delivered_by: string | null;
  delivered_by_display_name: string | null;
  order_status: string;
  order_cancelled_by: string | null;
  order_cancel_reason: string | null;
  created_at: Date | null;
  dispatched_at: Date | null;
  completed_at: Date | null;
  cancelled_at: Date | null;
  order_location: string | null;
  retailer_id: string;
};