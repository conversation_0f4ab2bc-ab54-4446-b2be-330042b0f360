import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Retailer } from '../../types/retailer';
import { fetchAllRetailers } from '../supabaseClient';

interface RetailerState {
  retailers: Retailer[];
  lastFetched: number | null;
  fetchRetailers: (forceRefresh?: boolean) => Promise<void>;
  getRetailerById: (id: string) => Retailer | undefined;
  updateRetailerInStore: (updated: Retailer) => void;
  deleteRetailerFromStore: (retailer_id: string) => void;
  searchRetailers: (query: string) => Retailer[];
}

/**
 * Zustand store for managing retailer data.
 * Data is persisted to local storage.
 */
export const useRetailerStore = create<RetailerState>()(
  persist(
    (set, get) => ({
      retailers: [],
      lastFetched: null,
      /**
       * Fetches retailers from Supabase and updates the store.
       * Only fetches if data is stale (e.g., more than 1 hour old) or forced.
       */
      fetchRetailers: async (forceRefresh = false) => {
        const { lastFetched } = get();
        const now = Date.now();
        const ONE_HOUR_MS = 60 * 60 * 1000; // 1 hour in milliseconds

        if (!forceRefresh && lastFetched && (now - lastFetched < ONE_HOUR_MS)) {
          console.log('Retailers data is fresh, using cached data.');
          return; // Use cached data if less than 1 hour old
        }

        try {
          console.log('Fetching retailers from Supabase...');
          const data = await fetchAllRetailers();
          if (data) {
            set({ retailers: data, lastFetched: now });
            console.log(`Fetched ${data.length} retailers.`);
          }
        } catch (error) {
          console.error('Failed to fetch retailers:', error);
          // Optionally, set an error state here
        }
      },
      /**
       * Retrieves a retailer by their ID.
       * @param {string} id - The ID of the retailer.
       * @returns {Retailer | undefined} The retailer object or undefined if not found.
       */
      getRetailerById: (id: string) => {
        return get().retailers.find(retailer => retailer.retailer_id === id);
      },
      /**
       * Updates a retailer in the store.
       * @param {Retailer} updated - The updated retailer object.
       */
      updateRetailerInStore: (updated: Retailer) => {
        set(state => ({
          retailers: state.retailers.map(r => r.retailer_id === updated.retailer_id ? { ...r, ...updated } : r)
        }));
      },
      /**
       * Deletes a retailer from the store.
       * @param {string} retailer_id - The ID of the retailer to delete.
       */
      deleteRetailerFromStore: (retailer_id: string) => {
        set(state => ({
          retailers: state.retailers.filter(r => r.retailer_id !== retailer_id)
        }));
      },
      /**
       * Searches retailers by name or location.
       * @param {string} query - The search query string.
       * @returns {Retailer[]} An array of matching retailer objects.
       */
      searchRetailers: (query: string) => {
        const lowerCaseQuery = query.toLowerCase();
        return get().retailers.filter(
          retailer =>
            retailer.retailer_name.toLowerCase().includes(lowerCaseQuery) ||
            (retailer.retailer_location && retailer.retailer_location.toLowerCase().includes(lowerCaseQuery))
        );
      },
    }),
    {
      name: 'retailer-storage', // unique name
      storage: createJSONStorage(() => localStorage), // Use localStorage for persistence
    }
  )
);