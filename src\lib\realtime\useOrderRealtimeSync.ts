import { useEffect } from 'react';
import { subscribeToOrderUpdates } from './publishOrderUpdate';
import { fetchOrderById } from '@/lib/api/orders';
import { useOrderStore } from '../store/orderStore';

/**
 * React hook to subscribe to Ably order updates and update Zustand store.
 */
export function useOrderRealtimeSync() {
  useEffect(() => {
    const unsubscribe = subscribeToOrderUpdates(async (orderId, action) => {
      // Fetch the latest order data
      const order = await fetchOrderById(orderId);
      if (order) {
        useOrderStore.getState().updateOrder(order); // assumes updateOrder exists in your store
      }
    });
    return unsubscribe;
  }, []);
}
