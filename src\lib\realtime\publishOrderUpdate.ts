import ably from './ablyClient';

/**
 * Publishes an order update event to the Ably channel.
 * @param orderId The order ID that was created/updated.
 * @param action The action performed (e.g., 'created', 'confirmed', 'completed').
 */
export function publishOrderUpdate(orderId: string, action: 'created' | 'confirmed' | 'completed') {
  const channel = ably.channels.get('orders');
  channel.publish('order-update', { orderId, action });
}

/**
 * Subscribes to order update events and calls the callback with the orderId and action.
 * @param callback Function to call when an order update event is received.
 * @returns Unsubscribe function.
 */
export function subscribeToOrderUpdates(callback: (orderId: string, action: string) => void) {
  const channel = ably.channels.get('orders');
  const handler = (msg: any) => {
    const { orderId, action } = msg.data;
    callback(orderId, action);
  };
  channel.subscribe('order-update', handler);
  return () => channel.unsubscribe('order-update', handler);
}
