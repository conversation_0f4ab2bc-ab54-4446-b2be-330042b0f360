"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useRetailerStore } from "@/lib/store/retailerStore";
import { ScrollArea } from "@/components/ui/scroll-area";

interface RetailerComboboxProps {
  selectedRetailerId: string | null;
  onSelectRetailer: (retailerId: string | null, retailerName: string | null) => void;
}

/**
 * A combobox component for selecting retailers.
 * Fetches retailers from the `useRetailerStore` and allows searching.
 * @param {RetailerComboboxProps} props - The component props.
 * @returns {JSX.Element} The rendered combobox.
 */
export function RetailerCombobox({ selectedRetailerId, onSelectRetailer }: RetailerComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const { retailers, fetchRetailers, getRetailerById, searchRetailers } = useRetailerStore();
  const [searchValue, setSearchValue] = React.useState("");

  React.useEffect(() => {
    fetchRetailers();
  }, [fetchRetailers]);

  const displayedValue = selectedRetailerId ? getRetailerById(selectedRetailerId)?.retailer_name : "Select retailer...";

  const filteredRetailers = searchValue ? searchRetailers(searchValue) : retailers;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between"
        >
          {displayedValue}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput
            placeholder="Search retailer..."
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            <CommandEmpty>No retailer found.</CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-48 overflow-y-auto">
                {filteredRetailers.map((retailer) => (
                  <CommandItem
                    key={retailer.retailer_id}
                    value={retailer.retailer_name}
                    onSelect={() => {
                      onSelectRetailer(retailer.retailer_id, retailer.retailer_name);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedRetailerId === retailer.retailer_id
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {retailer.retailer_name}
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}