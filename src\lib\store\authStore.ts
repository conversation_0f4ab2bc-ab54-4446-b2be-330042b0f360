import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Define a User type for auth
export interface User {
  id?: string;
  email?: string;
  [key: string]: unknown;
}

interface AuthState {
  user: User | null;
  role: string | null;
  fullName: string | null;
  setUser: (user: User | null) => void;
  setRole: (role: string | null) => void;
  setFullName: (fullName: string | null) => void;
  clearAuth: () => void;
  _hasHydrated: boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      role: null,
      fullName: null,
      _hasHydrated: false,
      setUser: (user) => set({ user }),
      setRole: (role) => set({ role }),
      setFullName: (fullName) => set({ fullName }),
      clearAuth: () => set({ user: null, role: null, fullName: null }),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      onRehydrateStorage: () => (state) => {
        console.log('AuthStore: onRehydrateStorage called');
        if (state) {
          state._hasHydrated = true;
          console.log('AuthStore: _hasHydrated set to true, state:', state);
        } else {
          console.log('AuthStore: state is null or undefined during rehydration');
        }
      },
    }
  )
);