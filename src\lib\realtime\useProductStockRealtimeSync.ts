import { useEffect } from 'react';
import { subscribeToProductStockUpdates } from '@/lib/realtime/productStockRealtime';
import { fetchProductsByIds } from '@/lib/api/products';
import { useInventoryStore } from '@/lib/store/inventoryStore';

/**
 * React hook to subscribe to Ably product stock updates and update Zustand store.
 */
export function useProductStockRealtimeSync() {
  useEffect(() => {
    const unsubscribe = subscribeToProductStockUpdates(async (productIds) => {
      const products = await fetchProductsByIds(productIds);
      if (products && products.length > 0) {
        // Update each product in the inventory store
        const store = useInventoryStore.getState();
        products.forEach((product) => {
          store.updateInventoryItem(product.product_id, product);
        });
      }
    });
    return unsubscribe;
  }, []);
}
