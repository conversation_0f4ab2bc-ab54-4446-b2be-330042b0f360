import { supabase } from '@/lib/supabaseClient';

/**
 * Logs in a user with email and password.
 * @param {string} email - The user's email.
 * @param {string} password - The user's password.
 * @returns {Promise<object>} The Supabase sign-in response.
 */
export async function signIn(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  if (error) throw error;
  return data;
}

/**
 * Logs out the current user.
 * @returns {Promise<object>} The Supabase sign-out response.
 */
export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
  return { success: true };
}

/**
 * Fetches the role of a staff member.
 * @param {string} userId - The ID of the user.
 * @returns {Promise<string | null>} The staff role or null if not found.
 */
export async function getStaffRole(userId: string) {
  const { data, error } = await supabase
    .from('staff_members')
    .select('staff_role')
    .eq('user_id', userId)
    .single();
  if (error) throw error;
  return data ? data.staff_role : null;
}

/**
 * Fetches the full name of a staff member.
 * @param {string} userId - The ID of the user.
 * @returns {Promise<string | null>} The staff's full name or null if not found.
 */
export async function getStaffFullName(userId: string) {
  const { data, error } = await supabase
    .from('staff_members')
    .select('full_name')
    .eq('user_id', userId)
    .single();
  if (error) throw error;
  return data ? data.full_name : null;
}