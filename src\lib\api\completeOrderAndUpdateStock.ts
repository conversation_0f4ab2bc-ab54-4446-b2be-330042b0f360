import { supabase } from '@/lib/supabaseClient';
import { Order } from '@/types/order';
import { publishInventoryUpdate } from '@/lib/realtime/publishInventoryUpdate';

/**
 * Completes an order: sets status to 'completed', sets completed_at, and updates product stock.
 * @param orderId - The order ID to complete.
 * @param billItems - Array of bill items for the order (must include product_id and quantity).
 * @returns {Promise<{ success: boolean; error?: string }>} Result of the operation.
 */
export async function completeOrderAndUpdateStock(orderId: string, billItems: { product_id: string; quantity: number }[]): Promise<{ success: boolean; error?: string }> {
  try {
    // 1. Update the order status and completed_at
    const { error: orderError } = await supabase
      .from('orders')
      .update({
        order_status: 'completed',
        completed_at: new Date().toISOString(),
      })
      .eq('order_id', orderId);
    if (orderError) {
      return { success: false, error: orderError.message };
    }

    // 2. Fetch current product data (include product_code)
    const productIds = billItems.map(item => item.product_id);
    // Fetch all fields to ensure product_code is present
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('product_id, product_code, quantity_out, quantity_reserved, quantity_left')
      .in('product_id', productIds);
    if (fetchError) {
      return { success: false, error: fetchError.message };
    }

    // 3. Prepare product updates
    const updates = billItems.map(item => {
      const product = products.find(p => p.product_id === item.product_id);
      if (!product) return null;
      if (!product.product_code) {
        console.error('Missing product_code for product:', product);
      }
      return {
        product_id: product.product_id,
        product_code: product.product_code || '',
        quantity_out: (product.quantity_out || 0) + item.quantity,
        quantity_reserved: (product.quantity_reserved || 0) - item.quantity,
        quantity_left: (product.quantity_left || 0) - item.quantity,
        updated_at: new Date().toISOString(),
      };
    }).filter(Boolean);

    // Log the prepared data for order completion
    console.log('Prepared data for order completion:', {
      order: {
        order_status: 'completed',
        completed_at: new Date().toISOString(),
      },
      products: updates,
    });

    // 4. Bulk update products
    if (updates.length > 0) {
      const { error: updateError } = await supabase
        .from('products')
        .upsert(updates, { onConflict: 'product_id' });
      if (updateError) {
        return { success: false, error: updateError.message };
      }
      // Publish Ably event for realtime inventory update
      publishInventoryUpdate(
        updates.filter((u): u is {
          product_id: string;
          product_code: string;
          quantity_out: number;
          quantity_reserved: number;
          quantity_left: number;
          updated_at: string;
        } => u !== null).map(u => u.product_id)
      );
    }

    return { success: true };
  } catch (err: any) {
    return { success: false, error: err.message || 'Unknown error' };
  }
}
