import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const { email, password, full_name, staff_role, phone_number, address } = req.body;
  if (!email || !password || !full_name || !staff_role || !phone_number || !address) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  // 1. Create user in Supabase Auth
  const { data: userData, error: signUpError } = await supabase.auth.admin.createUser({
    email,
    password,
    email_confirm: true,
  });
  if (signUpError || !userData?.user?.id) {
    return res.status(400).json({ error: signUpError?.message || 'Failed to create user' });
  }
  const user_id = userData.user.id;
  // 2. Insert into staff_members table
  const { error: insertError } = await supabase
    .from('staff_members')
    .insert({
      user_id,
      full_name,
      staff_role,
      phone_number,
      address,
    });
  if (insertError) {
    return res.status(400).json({ error: insertError.message });
  }
  return res.status(200).json({ success: true });
}
