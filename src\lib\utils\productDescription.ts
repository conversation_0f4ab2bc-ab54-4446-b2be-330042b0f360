import { InventoryItem } from "@/database/schema";

/**
 * Generates a descriptive string for an inventory item based on its properties.
 * This function is designed to be reusable across different parts of the application,
 * such as order creation or product display.
 * @param {InventoryItem} item - The inventory item.
 * @returns {string} The formatted product description.
 */
export const generateProductDescription = (item: InventoryItem): string => {
  const parts: string[] = [];

  if (item.product_category) {
    parts.push(item.product_category);
  }

  if (item.power_type) {
    parts.push(`(${item.power_type})`);
  }

  const lensParams: string[] = [];
  if (item.spherical_value !== null) {
    lensParams.push(`SPH: ${item.spherical_value > 0 ? '+' : ''}${item.spherical_value.toFixed(2)}`);
  }
  if (item.cylindrical_value !== null) {
    lensParams.push(`CYL: ${item.cylindrical_value > 0 ? '+' : ''}${item.cylindrical_value.toFixed(2)}`);
  }
  if (item.add_value !== null) {
    lensParams.push(`ADD: ${item.add_value > 0 ? '+' : ''}${item.add_value.toFixed(2)}`);
  }
  if (item.axis_value !== null) {
    lensParams.push(`AXIS: ${item.axis_value}`);
  }
  if (item.lens_side) {
    lensParams.push(item.lens_side);
  }

  if (lensParams.length > 0) {
    parts.push(`[${lensParams.join(', ')}]`);
  }

  return parts.join(' ');
};