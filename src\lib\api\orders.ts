import { Order } from '@/types/order';
import { OrderBillItem } from '@/types/order-bill-item';
import { supabase } from '@/lib/supabaseClient';
import { publishInventoryUpdate } from '@/lib/realtime/publishInventoryUpdate';

/**
 * @typedef {Object} NewOrder
 * @property {string} order_code - The unique code for the order.
 * @property {string} issued_by_display_name - The display name of the user who issued the order.
 * @property {string} order_issued_by - The ID of the user who issued the order.
 * @property {string} order_location - The location where the order was placed.
 * @property {string} order_status - The current status of the order (e.g., 'pending', 'completed').
 * @property {string} retailer_display_name - The display name of the retailer.
 * @property {string} retailer_id - The ID of the retailer.
 */
export type NewOrder = {
  order_code: string;
  issued_by_display_name: string;
  order_issued_by: string;
  order_location: string;
  order_status: string;
  retailer_display_name: string;
  retailer_id: string;
};

/**
 * @typedef {Object} NewOrderBillItem
 * @property {string} product_id - The ID of the product.
 * @property {string} product_description - The description of the product.
 * @property {number} quantity - The quantity of the product.
 * @property {number} price_per_unit - The price per unit of the product.
 * @property {string} order_id - The ID of the order to which this item belongs.
 */
export type NewOrderBillItem = {
  product_id: string;
  product_description: string;
  quantity: number;
  price_per_unit: number;
  order_id: string;
};

/**
 * Creates a new order in the 'orders' table.
 * @param {NewOrder} orderData - The data for the new order.
 * @returns {Promise<Order|null>} A promise that resolves to the created Order object or null if an error occurred.
 */
export async function createOrder(orderData: NewOrder): Promise<Order | null> {
  const { data, error } = await supabase
    .from('orders')
    .insert([orderData])
    .select();

  if (error) {
    console.error('Error creating order:', error.message);
    return null;
  }
  return data ? (data[0] as Order) : null;
}

/**
 * Creates new order bill items in the 'order_bill_items' table.
 * @param {string} orderId - The ID of the order to which these items belong.
 * @param {NewOrderBillItem[]} items - An array of order bill item data.
 * @returns {Promise<OrderBillItem[]|null>} A promise that resolves to the created OrderBillItem objects or null if an error occurred.
 */
export async function createOrderBillItems(orderId: string, items: NewOrderBillItem[]): Promise<OrderBillItem[] | null> {
  const itemsWithOrderId = items.map(item => ({ ...item, order_id: orderId }));
  const { data, error } = await supabase
    .from('order_bill_items')
    .insert(itemsWithOrderId)
    .select();

  if (error) {
    console.error('Error creating order bill items:', error.message);
    return null;
  }
  return data ? (data as OrderBillItem[]) : null;
}


/**
 * Fetches all orders from the 'orders' table, ordered by 'created_at' in descending order.
 * @returns {Promise<Order[]>} A promise that resolves to an array of Order objects.
 */
export async function fetchAllOrders(): Promise<Order[]> {

  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      retailer:retailer_id(retailer_name)
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching all orders:', error.message);
    return [];
  }
  return data as Order[];
}

/**
 * Fetches orders with pagination support and total count.
 * @param {number} offset - The number of rows to skip.
 * @param {number} limit - The maximum number of rows to return.
 * @param {string} status - Optional status filter ('all', 'pending', 'active', 'completed', 'cancelled').
 * @param {boolean} countOnly - If true, returns only the total count.
 * @returns {Promise<Order[] | number>} A promise that resolves to an array of Order objects or total count.
 */
export async function fetchOrdersPaginated(
  offset: number,
  limit: number,
  status: string = 'all',
  countOnly: boolean = false
): Promise<Order[] | number> {
  if (countOnly) {
    // Get total count
    let countQuery = supabase.from('orders').select('*', { count: 'exact' });

    if (status !== 'all') {
      if (status === 'active') {
        countQuery = countQuery.in('order_status', ['active', 'dispatched', 'waiting for delivery success']);
      } else {
        countQuery = countQuery.eq('order_status', status);
      }
    }

    const { count, error } = await countQuery;
    if (error) {
      console.error('Error fetching orders count:', error.message);
      return 0;
    }
    return count || 0;
  } else {
    // Get paginated data
    let dataQuery = supabase
      .from('orders')
      .select(`
        *,
        retailer:retailer_id(retailer_name)
      `);

    if (status !== 'all') {
      if (status === 'active') {
        dataQuery = dataQuery.in('order_status', ['active', 'dispatched', 'waiting for delivery success']);
      } else {
        dataQuery = dataQuery.eq('order_status', status);
      }
    }

    const { data, error } = await dataQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching orders:', error.message);
      return [];
    }
    return data as Order[];
  }
}

/**
 * Fetches active orders from the 'orders' table.
 * Active orders are defined as those with 'order_status' of 'dispatched' or 'waiting for delivery success'.
 * @returns {Promise<Order[]>} A promise that resolves to an array of active Order objects.
 */
export async function fetchActiveOrders(): Promise<Order[]> {

  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      retailer:retailer_id(retailer_name)
    `)
    .in('order_status', ['active', 'dispatched', 'waiting for delivery success'])
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching active orders:', error.message);
    return [];
  }
  return data as Order[];
}

/**
 * Fetches pending orders from the 'orders' table.
 * Pending orders are defined as those with 'order_status' of 'pending'.
 * @returns {Promise<Order[]>} A promise that resolves to an array of pending Order objects.
 */
export async function fetchPendingOrders(): Promise<Order[]> {

  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      retailer:retailer_id(retailer_name)
    `)
    .eq('order_status', 'pending')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching pending orders:', error.message);
    return [];
  }
  return data as Order[];
}

/**
 * Fetches completed orders from the 'orders' table.
 * Completed orders are defined as those with 'order_status' of 'completed'.
 * @returns {Promise<Order[]>} A promise that resolves to an array of completed Order objects.
 */
export async function fetchCompletedOrders(): Promise<Order[]> {

  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      retailer:retailer_id(retailer_name)
    `)
    .eq('order_status', 'completed')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching completed orders:', error.message);
    return [];
  }
  return data as Order[];
}

/**
 * Fetches cancelled orders from the 'orders' table.
 * Cancelled orders are defined as those with 'order_status' of 'cancelled'.
 * @returns {Promise<Order[]>} A promise that resolves to an array of cancelled Order objects.
 */
export async function fetchCancelledOrders(): Promise<Order[]> {

  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      retailer:retailer_id(retailer_name)
    `)
    .eq('order_status', 'cancelled')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching cancelled orders:', error.message);
    return [];
  }
  return data as Order[];
}

/**
 * Updates the status of an order in the 'orders' table.
 * @param {string} orderId - The ID of the order to update.
 * @param {string} newStatus - The new status to set (e.g., 'active').
 * @returns {Promise<boolean>} A promise that resolves to true if the update was successful, false otherwise.
 */
export async function updateOrderStatus(orderId: string, newStatus: string): Promise<boolean> {
  const { error } = await supabase
    .from('orders')
    .update({ order_status: newStatus })
    .eq('order_id', orderId);

  if (error) {
    console.error('Error updating order status:', error.message);
    return false;
  }
  return true;
}

/**
 * Deletes an order from the 'orders' table by order_id.
 * Also deletes related order_bill_items via ON DELETE CASCADE.
 * @param {string} orderId - The ID of the order to delete.
 * @returns {Promise<boolean>} A promise that resolves to true if the deletion was successful, false otherwise.
 */
export async function deleteOrder(orderId: string): Promise<boolean> {
  const { error } = await supabase
    .from('orders')
    .delete()
    .eq('order_id', orderId);

  if (error) {
    console.error('Error deleting order:', error.message);
    return false;
  }
  return true;
}

/**
 * Completes an order and updates stock levels in the database.
 * @param {string} orderId - The ID of the order to complete.
 * @returns {Promise<boolean>} A promise that resolves to true if the operation was successful, false otherwise.
 */
export async function completeOrderAndUpdateStock(orderId: string): Promise<boolean> {
  // 1. Get order bill items
  const { data: billItems, error: billError } = await supabase
    .from('order_bill_items')
    .select('product_id, quantity')
    .eq('order_id', orderId);
  if (billError || !billItems) {
    console.error('Order complete: Failed to fetch bill items', billError);
    return false;
  }

  // 1.5. Prepare product updates
  // Fetch all products involved
  const productIds = billItems.map(item => item.product_id);
  const { data: products, error: productsError } = await supabase
    .from('products')
    .select('product_id, product_code, quantity_out, quantity_reserved, quantity_left')
    .in('product_id', productIds);
  if (productsError || !products) {
    console.error('Order complete: Failed to fetch products', productsError);
    return false;
  }

  // Prepare updated product data (only required fields)
  const updatedProducts = products.map(product => {
    const billItem = billItems.find(item => item.product_id === product.product_id);
    if (!billItem) return null;
    return {
      product_id: product.product_id,
      product_code: product.product_code,
      quantity_out: (product.quantity_out || 0) + billItem.quantity, // increment quantity_out
      quantity_reserved: (product.quantity_reserved || 0) - billItem.quantity,
      quantity_left: (product.quantity_left || 0) - billItem.quantity,
    };
  }).filter(Boolean);

  // Prepare order update (only required fields)
  const orderUpdate = {
    order_status: 'complete',
    completed_at: new Date().toISOString(),
  };

  // Log only the required data
  console.log('prepared data for order completion', {
    order: orderUpdate,
    products: updatedProducts,
  });

  // Actually update products in DB
  if (updatedProducts.length > 0) {
    const { error: updateError } = await supabase
      .from('products')
      .upsert(updatedProducts, { onConflict: 'product_id' });
    if (updateError) {
      console.error('Error updating products during order completion:', updateError);
      return false;
    }
    // Update local Zustand inventory store immediately
    try {
      const { updateInventoryItem } = require('@/lib/store/inventoryStore').useInventoryStore.getState();
      updatedProducts.filter(Boolean).forEach(prod => {
        updateInventoryItem(prod!.product_id, prod!);
      });
    } catch (err) {
      console.warn('Could not update local inventory store:', err);
    }
    // Publish Ably event for inventory update
    publishInventoryUpdate(updatedProducts.map(p => p!.product_id));
  }

  // Update the order in DB
  const { error: orderUpdateError } = await supabase
    .from('orders')
    .update(orderUpdate)
    .eq('order_id', orderId);
  if (orderUpdateError) {
    console.error('Error updating order status to complete:', orderUpdateError);
    return false;
  }

  return true;
}

/**
 * Fetch a single order by its ID.
 * @param orderId The order ID to fetch.
 * @returns The order object or null if not found.
 */
export async function fetchOrderById(orderId: string) {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('order_id', orderId)
    .single();
  if (error) {
    console.error('Error fetching order by ID:', error.message);
    return null;
  }
  return data as Order;
}