# Inventory System Refactoring Plan

This document tracks the progress of refactoring the inventory data fetching and local storage system using Zustand and IndexedDB.

## Overall Task
Implement a bulk inventory fetching system after Supabase login that loads all products and their associated lens parameters using batched requests (1000 rows at a time). Store this data in a global Zustand store. Trigger the fetch in the background using `useEffect` after login, ideally from the layout or dashboard provider. Enable client-side filtering and pagination using Shadcn and TanStack Table. Implement IndexedDB for offline and local storage, ensuring data persistence and availability across page navigations.

## Task Done
- Brainstormed and separated tasks for the refactoring process.
- Identified the core components and their responsibilities.
- Outlined the integration of Zustand for global state management.
- Proposed the use of IndexedDB for offline data persistence.
- Confirmed offline data availability across page navigations with the proposed architecture.

## Task Next

### I. Core Data Fetching & Zustand Store Implementation

*   **Task 1: Define Zustand Store Structure** (`src/lib/store/inventoryStore.ts`)
    *   Created a new Zustand store to hold the inventory data (products and lens parameters).
    *   Defined the state shape: `inventoryItems: InventoryItem[]`, `isLoading: boolean`, `error: string | null`, `lastUpdated: Date | null`.
    *   Defined actions: `setInventoryItems`, `setLoading`, `setError`, `updateInventoryItem` (for stock updates), and a placeholder `fetchInventory` action.
    *   **STATUS: COMPLETED**

*   [x] **Task 5: Update InventoryItem Type Definition & Usage**
    *   Updated `InventoryItem` type in `src/database/schema.ts` to align `product_code` as number and include `product_id`.
    *   Adjusted `fetchInventoryData` in `src/lib/api/supabase.ts` and `updateInventoryItem` in `src/lib/store/inventoryStore.ts` to use `product_id` for consistency.

*   [x] **Task 6: Update Inventory Display Components**
    *   Updated display components (e.g., `columns.tsx`, `data-table.tsx`) to use the new `InventoryItem` type and display `product_code` and `quantity_left` correctly.

*   [x] **Task 2: Implement Batched Data Fetching Function**
    *   Created `fetchAllInventoryBatched` in `src/lib/api/supabase.ts` to fetch all inventory data in batches, utilizing `fetchInventoryData`.

*   [x] **Task 3: Integrate Fetching with Zustand Store**
    *   Modified the `fetchInventory` action in `src/lib/store/inventoryStore.ts` to call `fetchAllInventoryBatched` and manage `inventoryItems`, `isLoading`, and `error` states.

### II. Background Fetching & Initial Load

*   [x] **Task 4: Trigger Fetch on Login/App Load**
    *   Implemented `ZustandHydrationProvider` in `src/app/layout.tsx` to call `fetchInventory` on initial load.

### III. Client-Side Functionality (Filtering & Pagination)

*   **Task 5: Update `DataTable` Component** (`src/components/inventory_list/data-table.tsx`)
    *   The `DataTable` component will now consume data directly from the Zustand store.
    *   Implement client-side filtering using TanStack Table's filtering capabilities.
    *   Implement client-side pagination using TanStack Table's pagination features, removing the need for external pagination props.
    *   Integrate Shadcn UI components for filtering inputs and pagination controls.

### IV. Stock Updates & Real-time Sync

*   **Task 6: Implement Stock Update Logic**
    *   Identify where stock updates occur (likely after an order is placed, interacting with a separate `orders` table).
    *   When an order is processed, instead of re-fetching the entire inventory, dispatch an `updateInventoryItem` action to the Zustand store to immediately update the local stock count for the affected item(s).
    *   This might involve a Supabase trigger/function that notifies the client, or a direct client-side update after a successful order API call.

### V. Cleanup & Removal

*   **Task 7: Remove Obsolete Data Fetching Logic**
    *   The existing `fetchInventoryData` in `src/lib/api/supabase.ts` might be refactored or become a helper for the new batched fetch, but its direct usage for paginated display will be removed.
    *   Remove any `useState` or `useEffect` hooks in `src/app/dashboard/admin/inventory/page.tsx` or `src/components/inventory_list/data-table.tsx` that were previously managing pagination state or fetching data directly.
    *   Remove any `getPagination` utility functions or related pagination state management that are no longer needed due to client-side pagination.

### VI. IndexedDB Integration for Offline Storage

*   [x] **Task 9: Choose an IndexedDB Library**
    *   Used `Dexie.js` for IndexedDB interactions.
*   [x] **Task 10: Initialize IndexedDB and Define Schema**
    *   Set up the IndexedDB database with `inventoryItems` object store in `src/lib/database/indexeddb.ts`.
*   [x] **Task 11: Persist Zustand Store Data to IndexedDB**
    *   Implemented persistence of `inventoryItems` from Zustand store to IndexedDB using Dexie in `src/lib/store/inventoryStore.ts`.
*   [x] **Task 12: Load Data from IndexedDB on App Initialization (Offline First)**
    *   Modified `fetchInventory` in `src/lib/store/inventoryStore.ts` to first attempt loading inventory data from IndexedDB, then fetch from Supabase in the background to update the cache.

*   [x] **Task 13: Integrate Supabase Realtime for Inventory Updates**
    *   Subscribed to `products` and `lensparameters` table changes using Supabase Realtime in `src/lib/api/supabase.ts`.
    *   Integrated the subscription into `useInventoryStore` in `src/lib/store/inventoryStore.ts` to trigger a re-fetch and update the Zustand store and IndexedDB cache on changes.
*   [x] **Task 14: Integrate `useInventoryStore` into Inventory Page**
    *   Replaced `@tanstack/react-query` with `useInventoryStore` in `src/app/dashboard/admin/inventory/page.tsx` for inventory data fetching and state management.
    *   Implemented `useEffect` to call `fetchInventory` on component mount and subscribe to real-time updates, ensuring data consistency.
- **Task 15: Refine Data Synchronization and Conflicts**
  - **Status:** ✅ Completed
  - **Description:** Fixed an issue in `fetchAllInventoryBatched` in `src/lib/api/supabase.ts` to correctly handle batching for both products and lens parameters, ensuring `lensparameters` are fetched only for the `product_id`s present in the current product batch. Also, corrected the declaration of `hasMoreProducts` variable and improved error logging for `lensParametersError` by stringifying the error object. Added console logs in `src/lib/store/inventoryStore.ts` to verify the number of items loaded into Zustand and IndexedDB, and items loaded from cache on fetch error.

## Task Completed

## Bugs and its Fixes
- **Improved Error Logging for `lensParametersError`**: Modified `src/lib/api/supabase.ts` to stringify the `lensParametersError` object in `console.error` for more detailed debugging.
- Refactored `fetchAllInventoryBatched` in <mcfile name="supabase.ts" path="src/lib/api/supabase.ts"></mcfile> to only fetch product data. The individual fetching of lens parameters has been removed from this function.
- Introduced a new function <mcsymbol name="fetchLensParametersByProductId" filename="supabase.ts" path="src/lib/api/supabase.ts" startline="145" type="function"></mcsymbol> in <mcfile name="supabase.ts" path="src/lib/api/supabase.ts"></mcfile> to fetch lens parameters for a specific product ID on demand. This is part of the strategy to reduce initial load and target API calls.
- Planned implementation of IndexedDB for caching lens parameters to further optimize performance and reduce redundant API calls.
- **Fixed Syntax Error in `fetchAllInventoryBatched`**: Removed extraneous closing brace and duplicate return statement in `src/lib/api/supabase.ts`.