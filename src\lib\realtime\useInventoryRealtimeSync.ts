import { useEffect } from 'react';
import ably from '@/lib/realtime/ablyClient';
import { fetchProductsByIds } from '@/lib/api/products';
import { db } from '@/lib/database/indexeddb';
import { useInventoryStore } from '@/lib/store/inventoryStore';

export function useInventoryRealtimeSync() {
  useEffect(() => {
    const channel = ably.channels.get('inventory-updates');
    const onUpdate = async (msg: unknown) => {
      console.log('[Ably] inventory update event received:', msg);
      // Type guard for expected Ably message shape
      if (
        typeof msg === 'object' &&
        msg !== null &&
        'data' in msg &&
        typeof (msg as { data: unknown }).data === 'object' &&
        (msg as { data: unknown }).data !== null &&
        'productIds' in (msg as { data: { productIds?: unknown } }).data
      ) {
        const { productIds } = (msg as { data: { productIds: string[] } }).data;
        if (!productIds || !Array.isArray(productIds) || productIds.length === 0) return;
        // Always fetch the latest data for these products
        const updatedProducts = await fetchProductsByIds(productIds);

        // Handle updated/created products
        for (const prod of updatedProducts) {
          await db.inventoryItems.put(prod);
        }
        const { updateInventoryItem, setInventoryItems, inventoryItems } = useInventoryStore.getState();
        console.log('[Ably] updating inventory items:', updatedProducts.length);
        for (const prod of updatedProducts) {
          console.log('[Ably] updating product:', prod.product_id, prod);
          updateInventoryItem(prod.product_id, prod);
        }

        // Handle deleted products (products that were in productIds but not in updatedProducts)
        const updatedProductIds = updatedProducts.map(p => p.product_id);
        const deletedProductIds = productIds.filter(id => !updatedProductIds.includes(id));

        if (deletedProductIds.length > 0) {
          console.log('[Ably] removing deleted products:', deletedProductIds);
          // Remove from IndexedDB
          for (const productId of deletedProductIds) {
            await db.inventoryItems.delete(productId);
          }
          // Remove from Zustand store
          const remainingItems = inventoryItems.filter(item => !deletedProductIds.includes(item.product_id));
          setInventoryItems(remainingItems);
        }

        console.log('[Ably] local inventory updated for:', productIds);
      }
    };
    channel.subscribe('updated', onUpdate);
    return () => channel.unsubscribe('updated', onUpdate);
  }, []);
}
