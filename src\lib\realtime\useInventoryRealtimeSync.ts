import { useEffect } from 'react';
import ably from '@/lib/realtime/ablyClient';
import { fetchProductsByIds } from '@/lib/api/products';
import { db } from '@/lib/database/indexeddb';
import { useInventoryStore } from '@/lib/store/inventoryStore';

export function useInventoryRealtimeSync() {
  useEffect(() => {
    const channel = ably.channels.get('inventory-updates');

    const onUpdate = async (msg: unknown) => {
      console.log('[Ably] inventory update event received:', msg);
      // Type guard for expected Ably message shape
      if (
        typeof msg === 'object' &&
        msg !== null &&
        'data' in msg &&
        typeof (msg as { data: unknown }).data === 'object' &&
        (msg as { data: unknown }).data !== null &&
        'productIds' in (msg as { data: { productIds?: unknown } }).data
      ) {
        const { productIds } = (msg as { data: { productIds: string[] } }).data;
        if (!productIds || !Array.isArray(productIds) || productIds.length === 0) return;

        // Fetch the latest data for these products
        const updatedProducts = await fetchProductsByIds(productIds);
        for (const prod of updatedProducts) {
          await db.inventoryItems.put(prod);
        }

        // Update Zustand store with the latest data
        const { updateInventoryItem } = useInventoryStore.getState();
        console.log('[Ably] updating inventory items:', updatedProducts.length);
        for (const prod of updatedProducts) {
          console.log('[Ably] updating product:', prod.product_id, prod);
          updateInventoryItem(prod.product_id, prod);
        }
        console.log('[Ably] local inventory updated for:', productIds);
      }
    };

    const onDelete = async (msg: unknown) => {
      console.log('[Ably] inventory delete event received:', msg);
      // Type guard for expected Ably message shape
      if (
        typeof msg === 'object' &&
        msg !== null &&
        'data' in msg &&
        typeof (msg as { data: unknown }).data === 'object' &&
        (msg as { data: unknown }).data !== null &&
        'productIds' in (msg as { data: { productIds?: unknown } }).data
      ) {
        const { productIds } = (msg as { data: { productIds: string[] } }).data;
        if (!productIds || !Array.isArray(productIds) || productIds.length === 0) return;

        console.log('[Ably] removing deleted products:', productIds);

        // Remove from IndexedDB
        for (const productId of productIds) {
          await db.inventoryItems.delete(productId);
        }

        // Remove from Zustand store
        const { setInventoryItems, inventoryItems } = useInventoryStore.getState();
        console.log('[Ably] current inventoryItems length:', inventoryItems.length);
        const remainingItems = inventoryItems.filter(item => !productIds.includes(item.product_id));
        console.log('[Ably] after filtering, remainingItems length:', remainingItems.length);
        setInventoryItems(remainingItems);

        console.log('[Ably] local inventory deleted for:', productIds);
      }
    };

    channel.subscribe('updated', onUpdate);
    channel.subscribe('deleted', onDelete);

    return () => {
      channel.unsubscribe('updated', onUpdate);
      channel.unsubscribe('deleted', onDelete);
    };
  }, []);
}
