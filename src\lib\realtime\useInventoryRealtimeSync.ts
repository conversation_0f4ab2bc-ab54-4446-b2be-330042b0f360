import { useEffect } from 'react';
import ably from '@/lib/realtime/ablyClient';
import { fetchProductsByIds } from '@/lib/api/products';
import { db } from '@/lib/database/indexeddb';
import { useInventoryStore } from '@/lib/store/inventoryStore';

export function useInventoryRealtimeSync() {
  useEffect(() => {
    const channel = ably.channels.get('inventory-updates');
    const onUpdate = async (msg: unknown) => {
      console.log('[Ably] update event received:', msg);
      // Type guard for expected Ably message shape
      if (
        typeof msg === 'object' &&
        msg !== null &&
        'data' in msg &&
        typeof (msg as { data: unknown }).data === 'object' &&
        (msg as { data: unknown }).data !== null &&
        'productIds' in (msg as { data: { productIds?: unknown } }).data
      ) {
        const { productIds } = (msg as { data: { productIds: string[] } }).data;
        if (!productIds || !Array.isArray(productIds) || productIds.length === 0) return;
        // Always fetch the latest data for these products
        const updatedProducts = await fetchProductsByIds(productIds);
        for (const prod of updatedProducts) {
          await db.inventoryItems.put(prod);
        }
        // Update Zustand store with the latest data (in-place)
        const { updateInventoryItem } = useInventoryStore.getState();
        console.log('[Ably] updating inventory items:', updatedProducts.length);
        for (const prod of updatedProducts) {
          console.log('[Ably] updating product:', prod.product_id, prod);
          updateInventoryItem(prod.product_id, prod);
        }
        console.log('[Ably] local inventory updated for:', productIds);
      }
    };
    channel.subscribe('updated', onUpdate);
    return () => channel.unsubscribe('updated', onUpdate);
  }, []);
}
