import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { InventoryItem } from '@/database/schema';
import { useInventoryStore } from '@/lib/store/inventoryStore';
import { useToast } from '@/components/ui/toast';
import { updateProduct, deleteProduct } from '@/lib/api/products';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { publishInventoryUpdate, publishInventoryDelete } from '@/lib/realtime/publishInventoryUpdate';

interface InventoryActionDialogProps {
  product: InventoryItem;
  isOpen: boolean;
  onClose: () => void;
}

export const InventoryActionDialog: React.FC<InventoryActionDialogProps> = ({ product, isOpen, onClose }) => {
  const [addQty, setAddQty] = useState('');
  const [reduceQty, setReduceQty] = useState('');
  const [loading, setLoading] = useState(false);
  const { setInventoryItems, inventoryItems } = useInventoryStore(); // removed fetchInventory
  const { showToast } = useToast();

  const handleAdd = async () => {
    if (!addQty) return;
    setLoading(true);
    try {
      const newQty = (product.quantity_left || 0) + Number(addQty);
      const updated = await updateProduct({ ...product, quantity_left: newQty });
      setInventoryItems(inventoryItems.map(item => item.product_id === updated.product_id ? updated : item));
      publishInventoryUpdate([updated.product_id]);
      showToast('Quantity added successfully', 'success');
      setAddQty('');
      onClose();
    } catch {
      showToast('Failed to add quantity', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleReduce = async () => {
    if (!reduceQty) return;
    if (Number(reduceQty) > (product.quantity_left || 0)) {
      showToast('Cannot reduce more than quantity left', 'error');
      return;
    }
    setLoading(true);
    try {
      const newQty = Math.max(0, (product.quantity_left || 0) - Number(reduceQty));
      const updated = await updateProduct({ ...product, quantity_left: newQty });
      setInventoryItems(inventoryItems.map(item => item.product_id === updated.product_id ? updated : item));
      publishInventoryUpdate([updated.product_id]);
      showToast('Quantity reduced successfully', 'success');
      setReduceQty('');
      onClose();
    } catch {
      showToast('Failed to reduce quantity', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this product?')) return;
    setLoading(true);
    try {
      await deleteProduct(product.product_id);

      // Publish delete event - this will trigger real-time sync for all browsers including this one
      publishInventoryDelete([product.product_id]);
      showToast('Product deleted successfully', 'success');
      onClose();
    } catch {
      showToast('Failed to delete product', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => { if (!loading) onClose(); }}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Update Product</DialogTitle>
        </DialogHeader>
        <div className="mb-4">
          <table className="w-full text-sm border mb-4">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-800">
                <th className="font-semibold px-2 py-1">Code</th>
                <th className="font-semibold px-2 py-1">Category</th>
                <th className="font-semibold px-2 py-1">Type</th>
                <th className="font-semibold px-2 py-1">SPH</th>
                <th className="font-semibold px-2 py-1">CYL</th>
                <th className="font-semibold px-2 py-1">AXIS</th>
                <th className="font-semibold px-2 py-1">ADD</th>
                <th className="font-semibold px-2 py-1">Side</th>
                <th className="font-semibold px-2 py-1">Qty</th>
                <th className="font-semibold px-2 py-1">Restock</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="px-2 py-1">{product.product_code}</td>
                <td className="px-2 py-1">{product.product_category}</td>
                <td className="px-2 py-1">{product.power_type}</td>
                <td className="px-2 py-1">{product.spherical_value}</td>
                <td className="px-2 py-1">{product.cylindrical_value}</td>
                <td className="px-2 py-1">{product.axis_value}</td>
                <td className="px-2 py-1">{product.add_value}</td>
                <td className="px-2 py-1">{product.lens_side}</td>
                <td className="px-2 py-1">{product.quantity_left}</td>
                <td className="px-2 py-1">{product.restock_threshold}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className="flex gap-2 mb-2">
          <div className="flex-1">
            <label className="block text-sm font-medium">Add Quantity</label>
            <input type="text" value={addQty} onChange={e => {
              const val = e.target.value.replace(/[^0-9]/g, '');
              setAddQty(val);
            }} className="w-full border rounded px-2 py-1 text-right" placeholder="0" inputMode="numeric" pattern="[0-9]*" />
            <Button className="mt-2 w-full" onClick={handleAdd} disabled={loading || !addQty}>Add</Button>
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium">Reduce Quantity</label>
            <input type="text" value={reduceQty} onChange={e => {
              const val = e.target.value.replace(/[^0-9]/g, '');
              setReduceQty(val);
            }} className="w-full border rounded px-2 py-1 text-right" placeholder="0" inputMode="numeric" pattern="[0-9]*" />
            <Button className="mt-2 w-full" onClick={handleReduce} disabled={loading || !reduceQty}>Reduce</Button>
          </div>
        </div>
        <div className="m@b-2">
          <Button className="w-full" variant="destructive" onClick={handleDelete} disabled={loading}>Delete Product</Button>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose} disabled={loading}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
