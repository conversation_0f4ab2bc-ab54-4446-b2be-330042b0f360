"use client";

import * as React from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { bulkUpdateProductQuantity, bulkDeleteProducts, bulkReductProductQuantity } from '@/lib/api/products';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { InventoryItem } from "@/database/schema";
import { useInventoryStore } from '@/lib/store/inventoryStore';
import { generateProductDescription } from '@/lib/utils/productDescription';
import { useToast } from '@/components/ui/toast';
import { fetchProductsByIds } from '@/lib/api/products';
import { db } from '@/lib/database/indexeddb';
import { publishInventoryUpdate, publishInventoryDelete } from '@/lib/realtime/publishInventoryUpdate';

/**
 * Props for the SelectedProductsDialog component.
 * @property {boolean} isOpen - Controls the open state of the dialog.
 * @property {() => void} onOpenChange - Callback for when the open state changes.
 * @property {InventoryItem[]} products - The list of selected inventory items to display.
 */
interface SelectedProductsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  products: InventoryItem[];
  onClearSelection: () => void;
}

/**
 * Dialog component to display a list of selected inventory items.
 * @param {SelectedProductsDialogProps} props - The props for the component.
 * @returns {JSX.Element} The rendered dialog.
 */
export function SelectedProductsDialog({ isOpen, onOpenChange, products, onClearSelection }: SelectedProductsDialogProps): React.ReactElement {
  const { clearSelectedProducts, } = useInventoryStore();
  const { showToast } = useToast();
  const [bulkUpdateQuantity, setBulkUpdateQuantity] = React.useState<string>('');
  const [bulkReduceQuantity, setBulkReduceQuantity] = React.useState<string>('');
  const [showUpdateConfirm, setShowUpdateConfirm] = React.useState(false);
  const [showReduceConfirm, setShowReduceConfirm] = React.useState(false);
  const [pendingUpdate, setPendingUpdate] = React.useState(false);
  const [pendingReduce, setPendingReduce] = React.useState(false);
  const [pendingDelete, setPendingDelete] = React.useState(false);
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div>
            <DialogTitle>Selected Inventory Items</DialogTitle>
            <DialogDescription>
              A list of all currently selected products.
            </DialogDescription>
          </div>


        </DialogHeader>
        <div className="py-4">
          {products.length > 0 ? (
            <div className="max-h-[400px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product ID</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Quantity Left</TableHead>
                    <TableHead>Remove</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product.product_id}>
                      <TableCell>{product.product_code}</TableCell>
                      <TableCell>{generateProductDescription(product)}</TableCell>
                      <TableCell>{product.quantity_left}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            // Use the hook directly
                            const { setSelectedProducts } = useInventoryStore.getState();
                            setSelectedProducts(products.filter(p => p.product_id !== product.product_id));
                          }}
                          aria-label="Remove"
                        >
                          <span className="text-red-500 font-bold">&times;</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-center text-muted-foreground">No products selected.</p>
          )}
        </div>
        <DialogFooter className="flex flex-col sm:flex-col sm:space-x-0 sm:space-y-2 md:flex-row md:justify-between md:space-y-0">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <Input
                type="text"
                placeholder="Bulk update quantity"
                className="w-40"
                value={bulkUpdateQuantity}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*$/.test(value) || value === '') {
                    setBulkUpdateQuantity(value);
                  }
                }}
              />
              <Button
                onClick={() => setShowUpdateConfirm(true)}
                disabled={products.length === 0 || !bulkUpdateQuantity || isNaN(Number(bulkUpdateQuantity))}
              >
                Update
              </Button>
              <AlertDialog open={showUpdateConfirm} onOpenChange={setShowUpdateConfirm}>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirm Bulk Update?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to update quantity for {products.length} items?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel onClick={() => setShowUpdateConfirm(false)}>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async () => {
                        setPendingUpdate(true);
                        const productIds = products.map(p => p.product_id);
                        try {
                          await bulkUpdateProductQuantity(productIds, Number(bulkUpdateQuantity));
                          // Fetch only updated products and update IndexedDB and Zustand
                          const updatedProducts = await fetchProductsByIds(productIds);
                          for (const prod of updatedProducts) {
                            await db.inventoryItems.put(prod);
                          }
                          // Update Zustand store
                          const { setInventoryItems, inventoryItems, clearSelectedProducts } = useInventoryStore.getState();
                          setInventoryItems([
                            ...updatedProducts,
                            ...inventoryItems.filter(item => !productIds.includes(item.product_id)),
                          ]);
                          // Ensure Zustand and IndexedDB are fully in sync after update
                          const allItems = await db.inventoryItems.toArray();
                          setInventoryItems(allItems);
                          clearSelectedProducts();
                          onClearSelection();
                          setBulkUpdateQuantity('');
                          onOpenChange(false);
                          showToast(`${products.length} items updated successfully`, 'success');
                          
                          // Publish update event to Ably channel
                          publishInventoryUpdate(productIds);
                          
                        } catch {
                          showToast('Failed to update products', 'error');
                        } finally {
                          setPendingUpdate(false);
                          setShowUpdateConfirm(false);
                        }
                      }}
                      disabled={pendingUpdate}
                    >
                      Confirm Update
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            <div className="flex items-center space-x-2">
              <Input
                type="text"
                placeholder="Bulk reduce quantity"
                className="w-40"
                value={bulkReduceQuantity}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*$/.test(value) || value === '') {
                    setBulkReduceQuantity(value);
                  }
                }}
              />
              <Button
                onClick={() => setShowReduceConfirm(true)}
                disabled={products.length === 0 || !bulkReduceQuantity || isNaN(Number(bulkReduceQuantity)) || products.some(p => Number(bulkReduceQuantity) > (p.quantity_left || 0))}
              >
                Reduce
              </Button>
              <AlertDialog open={showReduceConfirm} onOpenChange={setShowReduceConfirm}>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirm Bulk Reduce?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to reduce quantity for {products.length} items?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel onClick={() => setShowReduceConfirm(false)}>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async () => {
                        setPendingReduce(true);
                        const productIds = products.map(p => p.product_id);
                        // Prevent reducing more than available for any product
                        if (products.some(p => Number(bulkReduceQuantity) > (p.quantity_left || 0))) {
                          showToast('Cannot reduce more than quantity left for one or more items', 'error');
                          setPendingReduce(false);
                          setShowReduceConfirm(false);
                          return;
                        }
                        try {
                          await bulkReductProductQuantity(productIds, Number(bulkReduceQuantity));
                          // Fetch only updated products and update IndexedDB and Zustand
                          const updatedProducts = await fetchProductsByIds(productIds);
                          for (const prod of updatedProducts) {
                            await db.inventoryItems.put(prod);
                          }
                          // Update Zustand store
                          const { setInventoryItems, inventoryItems, clearSelectedProducts } = useInventoryStore.getState();
                          setInventoryItems([
                            ...updatedProducts,
                            ...inventoryItems.filter(item => !productIds.includes(item.product_id)),
                          ]);
                          clearSelectedProducts();
                          onClearSelection();
                          setBulkReduceQuantity('');
                          onOpenChange(false);
                          showToast(`${products.length} items reduced successfully`, 'success');
                          
                          // Publish update event to Ably channel
                          publishInventoryUpdate(productIds);
                        } catch {
                          showToast('Failed to reduce products', 'error');
                        } finally {
                          setPendingReduce(false);
                          setShowReduceConfirm(false);
                        }
                      }}
                      disabled={pendingReduce}
                    >
                      Confirm Reduce
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
          <div className="flex flex-col mt-4 md:mt-0">
            <div className="flex space-x-2 mb-2">
              <Button
                onClick={() => {
                  console.log("Selected items for export: ", products.map(p => p.product_code).join(", "));
                }}
                disabled={products.length === 0}
                className="flex-grow"
              >
                Export
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" disabled={products.length === 0} className="flex-grow">
                    Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirm Bulk Delete?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete
                      the selected products from your inventory.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={async () => {
                        setPendingDelete(true);
                        const productIds = products.map(p => p.product_id);
                        try {
                          await bulkDeleteProducts(productIds);

                          showToast(`${products.length} items deleted successfully`, 'success');
                          clearSelectedProducts();
                          onClearSelection();
                          setBulkUpdateQuantity('');
                          setBulkReduceQuantity('');
                          onOpenChange(false);

                          // Publish delete event to Ably channel - this will trigger real-time sync for all browsers
                          publishInventoryDelete(productIds);
                        } catch {
                          showToast('Failed to delete products', 'error');
                        } finally {
                          setPendingDelete(false);
                        }
                      }}
                      disabled={pendingDelete}
                    >
                      Confirm Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                clearSelectedProducts();
                onClearSelection();
                setBulkUpdateQuantity('');
                setBulkReduceQuantity('');
                onOpenChange(false);
              }}
              disabled={products.length === 0}
              className="w-full"
            >
              Clear Selected List
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}