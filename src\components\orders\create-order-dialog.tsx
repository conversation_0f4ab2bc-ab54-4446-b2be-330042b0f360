"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { CreateOrderForm } from "./create-order-form";
import { generateOrderCode } from "@/lib/utils/orderCodeGenerator";
import { useAuthStore } from "@/lib/store/authStore";
import React from "react";

/**
 * @typedef {Object} CreateOrderDialogProps
 */

/**
 * Renders a dialog for creating a new order.
 * @param {CreateOrderDialogProps} props - The component props.
 * @returns {JSX.Element} The rendered dialog.
 */
export function CreateOrderDialog() {
  const [orderCode, setOrderCode] = React.useState("");
  const [issuedBy, setIssuedBy] = React.useState("");

  const { fullName } = useAuthStore();

  const [isOpen, setIsOpen] = React.useState(false);

  React.useEffect(() => {
    if (fullName) {
      setIssuedBy(fullName);
    }
  }, [fullName]);

  React.useEffect(() => {
    if (isOpen) {
      setOrderCode(generateOrderCode());
    }
  }, [isOpen]);
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Create New Order</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[1500px]">
        <DialogHeader>
          <DialogTitle>Create New Order ({orderCode})</DialogTitle>
          <DialogDescription>
            Issued By: {issuedBy}
          </DialogDescription>
        </DialogHeader>
        <CreateOrderForm
          orderCode={orderCode}
          issuedBy={issuedBy}
          onOrderCreated={() => setIsOpen(false)}
        />
      </DialogContent>
    </Dialog>
  );
}