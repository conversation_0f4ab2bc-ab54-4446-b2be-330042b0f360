/**
 * @file order-search-results.tsx
 * @description Component to display search results in a table.
 */

import React from 'react';
import { InventoryItem } from '@/database/schema';
import { generateProductDescription } from '@/lib/utils/productDescription';

/**
 * @interface OrderSearchResultsProps
 * @property {{ items: InventoryItem[], totalCount: number }} searchResults - The search results containing items and total count.
 */
interface OrderSearchResultsProps {
  searchResults: { items: InventoryItem[]; totalCount: number };
  onAddToOrder: (item: InventoryItem) => void;
  className?: string;
}

/**
 * Renders a table for displaying search results.
 * @param {OrderSearchResultsProps} props - The component props.
 * @returns {JSX.Element} The search results table component.
 */
export function OrderSearchResults({ searchResults, onAddToOrder, className }: OrderSearchResultsProps): React.ReactElement {
  return (
    <div className={`w-full p-4 border rounded-md ${className}`}>
      <h3 className="text-lg font-semibold mb-4 flex items-center">
        Search Results
        {searchResults.totalCount > 5 && (
          <span className="ml-2 text-red-500 text-xs font-normal">
            ({searchResults.totalCount} results found! Only showing first 5 results. Please refine your search.)
          </span>
        )}
      </h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="w-1/6 px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Code</th>
              <th scope="col" className="w-3/6 px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Description</th>
              <th scope="col" className="w-1/6 px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider">Quantity Left</th>
              <th scope="col" className="w-1/6 px-6 py-3 text-left text-xs font-medium text-gray-500 tracking-wider"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {searchResults.items.length > 0 ? (
              searchResults.items.map((item: InventoryItem) => (
                <tr key={item.product_id}>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">{item.product_code}</td>
                  <td className="px-6 py-4 text-sm text-gray-500">{generateProductDescription(item)}</td>
                  <td className="px-6 py-4 text-sm text-gray-500">{item.quantity_left}</td>
                  <td className="px-6 py-4 text-right text-sm font-medium">
                    {(item.quantity_left ?? 0) > 0 ? (
                      <button
                        type="button"
                        className="text-indigo-600 hover:text-indigo-900"
                        onClick={() => onAddToOrder(item)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                      </button>
                    ) : (
                      <span className="text-gray-400 text-xs" title="Not enough quantity to be added">
                        out of stock
                      </span>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                  No search results to display.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}