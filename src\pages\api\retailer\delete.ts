import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const { retailer_id } = req.body;
  if (!retailer_id) {
    return res.status(400).json({ error: 'Missing retailer_id' });
  }
  const { error } = await supabase
    .from('retailer')
    .delete()
    .eq('retailer_id', retailer_id);
  if (error) return res.status(400).json({ error: error.message });
  return res.status(200).json({ success: true });
}
