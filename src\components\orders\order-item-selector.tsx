"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { XCircle } from "lucide-react";

/**
 * @typedef {Object} OrderItem
 * @property {string} description - The description of the item.
 * @property {number} quantity - The quantity of the item.
 * @property {number} price - The price per unit of the item.
 */

/**
 * @typedef {Object} OrderItemSelectorProps
 * @property {OrderItem[]} items - The list of order items.
 * @property {(items: OrderItem[]) => void} onItemsChange - Callback when items change.
 */

/**
 * Renders a component for selecting and managing order items.
 * @param {OrderItemSelectorProps} props - The component props.
 * @returns {JSX.Element} The rendered component.
 */
interface OrderItem {
  quantity: number;
  price: number;
  [key: string]: unknown;
}

interface OrderItemSelectorProps {
  items: OrderItem[];
  onItemsChange: (items: OrderItem[]) => void;
}

export function OrderItemSelector({ items, onItemsChange }: OrderItemSelectorProps) {
  const handleRemoveItem = (index: number) => {
    const newItems = items.filter((_, i) => i !== index);
    onItemsChange(newItems);
  };
  const handleItemChange = (index: number, field: string, value: unknown) => {
    const newItems = items.map((item, i) =>
      i === index ? { ...item, [field]: value } : item
    );
    onItemsChange(newItems);
  };
  const totalCost = items.reduce((sum: number, item: OrderItem) => sum + item.quantity * item.price, 0);

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]">#</TableHead>
            <TableHead>Item Description</TableHead>
            <TableHead className="w-[100px]">Quantity</TableHead>
            <TableHead className="w-[100px]">Price</TableHead>
            <TableHead className="w-[100px]">Total</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="min-h-[250px] overflow-y-auto">
          {items.map((item: OrderItem, index: number) => (
            <TableRow key={index}>
              <TableCell>{index + 1}</TableCell>
              <TableCell>
                <Input
                  value={typeof item.description === 'string' ? item.description : ''}
                  onChange={(e) => handleItemChange(index, "description", e.target.value)}
                />
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  value={item.quantity}
                  onChange={(e) => handleItemChange(index, "quantity", parseFloat(e.target.value))}
                />
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  value={item.price}
                  onChange={(e) => handleItemChange(index, "price", parseFloat(e.target.value))}
                />
              </TableCell>
              <TableCell>{(item.quantity * item.price).toFixed(2)}</TableCell>
              <TableCell>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveItem(index)}
                  disabled={items.length === 1} // Disable remove if only one item
                >
                  <XCircle className="h-4 w-4 text-red-500" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <div className="flex justify-end items-center mt-4">
        <div className="text-lg font-bold">
          Total Cost: ${totalCost.toFixed(2)}
        </div>
      </div>
    </div>
  );
}