/**
 * @module @/lib/hooks/useProductCount
 * @description React Query hook to fetch the total count of products from Supabase.
 */

import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/api/supabase';

/**
 * Custom hook to fetch the total count of products.
 * @returns {object} React Query result object containing data, error, isLoading, etc.
 */
export const useProductCount = () => {
  return useQuery({
    queryKey: ['productCount'],
    queryFn: async () => {
      const { count, error } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true });

      if (error) throw error;
      return count || 0;
    },
    staleTime: 1000 * 60 * 60, // 1 hour
  });
};