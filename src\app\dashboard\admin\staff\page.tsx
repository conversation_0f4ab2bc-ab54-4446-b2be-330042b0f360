'use client';
import { useEffect, useState, useRef, useCallback } from "react";
import { useStaffStore } from '@/lib/store/staffStore';
import { StaffMember } from "@/database/schema";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { createStaffMember, updateStaffMember, deleteStaffMember } from "@/lib/api/staff";
import { Dialog as ConfirmDialog, DialogContent as ConfirmDialogContent, DialogHeader as ConfirmDialogHeader, DialogTitle as ConfirmDialogTitle, DialogFooter as ConfirmDialogFooter } from "@/components/ui/dialog";

const PAGE_SIZE = 20;

const ROLES = [
  { value: "admin", label: "Admin" },
  { value: "reception", label: "Reception" },
  { value: "finance", label: "Finance" },
  { value: "delivery", label: "Delivery" },
];

function CreateStaffDialog({ onCreated }: { onCreated?: () => void }) {
  const [open, setOpen] = useState(false);
  const [form, setForm] = useState({
    email: "",
    password: "",
    full_name: "",
    staff_role: ROLES[0].value,
    phone_number: "",
    address: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const firstInputRef = useRef<HTMLInputElement>(null);

  const handleOpenChange = useCallback((v: boolean) => {
    setOpen(v);
    setError(null);
    setSuccess(false);
    if (v) setTimeout(() => firstInputRef.current?.focus(), 100);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm(f => ({ ...f, [e.target.name]: e.target.value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);
    const res = await createStaffMember(form);
    setLoading(false);
    if (res.success) {
      setSuccess(true);
      setForm({ email: "", password: "", full_name: "", staff_role: ROLES[0].value, phone_number: "", address: "" });
      onCreated?.();
      setTimeout(() => setOpen(false), 1200);
    } else {
      setError(res.error || "Failed to create staff member");
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <Button onClick={() => setOpen(true)} className="mb-4">+ New Staff Member</Button>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Staff Member</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input ref={firstInputRef} name="email" type="email" placeholder="Email" value={form.email} onChange={handleChange} required autoFocus />
          <Input name="password" type="password" placeholder="Password" value={form.password} onChange={handleChange} required minLength={6} />
          <Input name="full_name" placeholder="Full Name" value={form.full_name} onChange={handleChange} required />
          <select name="staff_role" value={form.staff_role} onChange={handleChange} className="w-full border rounded px-2 py-2">
            {ROLES.map(r => <option key={r.value} value={r.value}>{r.label}</option>)}
          </select>
          <Input name="phone_number" placeholder="Phone Number" value={form.phone_number} onChange={handleChange} required />
          <Input name="address" placeholder="Address" value={form.address} onChange={handleChange} required />
          {error && <div className="text-red-500 text-sm">{error}</div>}
          {success && <div className="text-green-600 text-sm">Staff member created!</div>}
          <DialogFooter>
            <Button type="submit" disabled={loading}>{loading ? "Creating..." : "Create"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

function EditStaffDialog({ staff, open, onOpenChange, onUpdated }: { staff: StaffMember; open: boolean; onOpenChange: (v: boolean) => void; onUpdated: () => void }) {
  const updateStaffInStore = useStaffStore(state => state.updateStaffInStore);
  const [form, setForm] = useState({
    full_name: staff.full_name || "",
    staff_role: staff.staff_role || ROLES[0].value,
    phone_number: staff.phone_number || "",
    address: staff.address || "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    setForm({
      full_name: staff.full_name || "",
      staff_role: staff.staff_role || ROLES[0].value,
      phone_number: staff.phone_number || "",
      address: staff.address || "",
    });
    setError(null);
    setSuccess(false);
  }, [staff, open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm(f => ({ ...f, [e.target.name]: e.target.value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);
    // Always send all required fields for update
    const updateData = {
      full_name: form.full_name,
      staff_role: form.staff_role,
      phone_number: form.phone_number,
      address: form.address,
    };
    // Log the data being sent to the API
    console.log('[EditStaffDialog] Submitting update:', { user_id: staff.user_id, ...updateData });
    const res = await updateStaffMember(staff.user_id, updateData);
    setLoading(false);
    if (res.success) {
      setSuccess(true);
      updateStaffInStore({ ...staff, ...updateData });
      onUpdated();
      setTimeout(() => onOpenChange(false), 1000);
    } else {
      setError(res.error || "Failed to update staff member");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Staff Member</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="full_name" placeholder="Full Name" value={form.full_name} onChange={handleChange} required />
          <select name="staff_role" value={form.staff_role} onChange={handleChange} className="w-full border rounded px-2 py-2">
            {ROLES.map(r => <option key={r.value} value={r.value}>{r.label}</option>)}
          </select>
          <Input name="phone_number" placeholder="Phone Number" value={form.phone_number} onChange={handleChange} required />
          <Input name="address" placeholder="Address" value={form.address} onChange={handleChange} required />
          {error && <div className="text-red-500 text-sm">{error}</div>}
          {success && <div className="text-green-600 text-sm">Staff member updated!</div>}
          <DialogFooter>
            <Button type="submit" disabled={loading}>{loading ? "Saving..." : "Save"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default function AdminStaffPage() {
  const { staff, fetchStaff, deleteStaffFromStore } = useStaffStore();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletingStaff, setDeletingStaff] = useState<StaffMember | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Sorting state
  const [sortAsc, setSortAsc] = useState(true);
  const [sortBy, setSortBy] = useState<'full_name' | 'staff_role' | 'phone_number' | 'address'>('full_name');

  // Sort staff before paginating
  const sortedStaff = [...staff].sort((a, b) => {
    const aVal = (a[sortBy] || '').toString().toLowerCase();
    const bVal = (b[sortBy] || '').toString().toLowerCase();
    if (aVal < bVal) return sortAsc ? -1 : 1;
    if (aVal > bVal) return sortAsc ? 1 : -1;
    return 0;
  });
  const totalPages = Math.ceil(staff.length / PAGE_SIZE);
  const paginatedStaff = sortedStaff.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

  const refreshStaff = useCallback(() => {
    setLoading(true);
    fetchStaff()
      .then(() => setLoading(false))
      .catch((e) => setError(e.message || "Failed to fetch staff"))
      .finally(() => setLoading(false));
  }, [fetchStaff]);

  const handleHardRefresh = async () => {
    setLoading(true);
    setError(null);
    try {
      await fetchStaff(true); // forceRefresh = true
    } catch (e: unknown) {
      setError(e instanceof Error ? e.message : 'Failed to refresh staff');
    }
    setLoading(false);
  };

  // Fetch staff if not loaded
  useEffect(() => {
    if (staff.length === 0) {
      fetchStaff();
    }
  }, [staff.length, fetchStaff]);

  // Set loading to false as soon as staff data is available
  useEffect(() => {
    if (staff.length > 0) {
      setLoading(false);
    }
  }, [staff.length]);

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">Admin Staff Management</h2>
        <Button variant="outline" onClick={handleHardRefresh}>Refresh staff data</Button>
      </div>
      <CreateStaffDialog onCreated={refreshStaff} />
      {selectedStaff && (
        <EditStaffDialog
          staff={selectedStaff}
          open={editDialogOpen}
          onOpenChange={(v) => {
            setEditDialogOpen(v);
            if (!v) setSelectedStaff(null);
          }}
          onUpdated={refreshStaff}
        />
      )}
      <ConfirmDialog open={deleteDialogOpen} onOpenChange={(v) => {
        setDeleteDialogOpen(v);
        if (!v) setDeletingStaff(null);
        setDeleteError(null);
      }}>
        <ConfirmDialogContent className="max-w-md">
          <ConfirmDialogHeader>
            <ConfirmDialogTitle>Delete Staff Member</ConfirmDialogTitle>
          </ConfirmDialogHeader>
          <div className="py-2">Are you sure you want to delete <span className="font-semibold">{deletingStaff?.full_name}</span>? This action cannot be undone.</div>
          {deleteError && <div className="text-red-500 text-sm">{deleteError}</div>}
          <ConfirmDialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={deleteLoading}>Cancel</Button>
            <Button variant="destructive" onClick={async () => {
              if (!deletingStaff) return;
              setDeleteLoading(true);
              setDeleteError(null);
              const res = await deleteStaffMember(deletingStaff.user_id);
              setDeleteLoading(false);
              if (res.success) {
                deleteStaffFromStore(deletingStaff.user_id);
                setDeleteDialogOpen(false);
                setDeletingStaff(null);
                // Optionally: refreshStaff();
              } else {
                setDeleteError(res.error || "Failed to delete staff member");
              }
            }} disabled={deleteLoading}>{deleteLoading ? "Deleting..." : "Delete"}</Button>
          </ConfirmDialogFooter>
        </ConfirmDialogContent>
      </ConfirmDialog>
      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p className="text-red-500">{error}</p>
      ) : (
        <div className="overflow-x-auto">
          <div className="max-h-[38rem] overflow-y-auto border rounded-xl shadow-lg bg-white dark:bg-gray-900">
            <table className="min-w-full text-sm border-separate border-spacing-0">
              <thead className="sticky top-0 z-10 bg-gray-800">
                <tr>
                  <th className="border-b border-r px-4 py-3 text-left text-white font-semibold w-1/4 cursor-pointer" onClick={() => { setSortBy('full_name'); setSortAsc(sortBy === 'full_name' ? !sortAsc : true); }}>
                    Full Name
                    <span className="ml-1">{sortBy === 'full_name' ? (sortAsc ? '▲' : '▼') : ''}</span>
                  </th>
                  <th className="border-b border-r px-4 py-3 text-left text-white font-semibold w-1/4 cursor-pointer" onClick={() => { setSortBy('staff_role'); setSortAsc(sortBy === 'staff_role' ? !sortAsc : true); }}>
                    Role
                    <span className="ml-1">{sortBy === 'staff_role' ? (sortAsc ? '▲' : '▼') : ''}</span>
                  </th>
                  <th className="border-b border-r px-4 py-3 text-left text-white font-semibold w-1/4 cursor-pointer" onClick={() => { setSortBy('phone_number'); setSortAsc(sortBy === 'phone_number' ? !sortAsc : true); }}>
                    Phone
                    <span className="ml-1">{sortBy === 'phone_number' ? (sortAsc ? '▲' : '▼') : ''}</span>
                  </th>
                  <th className="border-b border-r px-4 py-3 text-left text-white font-semibold w-1/4 cursor-pointer" onClick={() => { setSortBy('address'); setSortAsc(sortBy === 'address' ? !sortAsc : true); }}>
                    Address
                    <span className="ml-1">{sortBy === 'address' ? (sortAsc ? '▲' : '▼') : ''}</span>
                  </th>
                  <th className="border-b px-4 py-3 text-left text-white font-semibold w-32">Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedStaff.map((s, idx) => (
                  <tr key={s.user_id} className={
                    `hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors` +
                    (idx !== paginatedStaff.length - 1 ? ' border-b' : '')
                  }>
                    <td className="px-4 py-3 border-r align-top whitespace-nowrap">{s.full_name ?? "-"}</td>
                    <td className="px-4 py-3 border-r align-top whitespace-nowrap">{s.staff_role ?? "-"}</td>
                    <td className="px-4 py-3 border-r align-top whitespace-nowrap">{s.phone_number ?? "-"}</td>
                    <td className="px-4 py-3 border-r align-top whitespace-nowrap">{s.address ?? "-"}</td>
                    <td className="px-4 py-3 align-top whitespace-nowrap flex gap-2">
                      <Button size="sm" variant="outline" onClick={() => { setSelectedStaff(s); setEditDialogOpen(true); }}>Edit</Button>
                      <Button size="sm" variant="destructive" onClick={() => { setDeletingStaff(s); setDeleteDialogOpen(true); }}>Delete</Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="flex justify-center items-center mt-4 space-x-2">
            <button
              onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 rounded bg-gray-200 disabled:opacity-50"
            >
              Prev
            </button>
            <span>Page {currentPage} of {totalPages}</span>
            <button
              onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 rounded bg-gray-200 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}