import { supabase } from '@/lib/supabaseClient';
import { Staff<PERSON><PERSON>ber, Retailer } from '@/database/schema';

export async function fetchStaffMembers(): Promise<StaffMember[]> {
  const { data, error } = await supabase
    .from('staff_members')
    .select('*');
  if (error) throw error;
  return data as StaffMember[];
}

export async function fetchRetailers(): Promise<Retailer[]> {
  const { data, error } = await supabase
    .from('retailer')
    .select('*');
  if (error) throw error;
  return data as Retailer[];
}
