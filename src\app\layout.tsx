'use client'
import './globals.css';
import { ToastProvider } from '@/components/ui/toast';

/**
 * @module @/app/layout
 * @description Root layout component for the application.
 */

/**
 * Component to handle Zustand store hydration and initial data fetching.
 * @returns {JSX.Element} The children components.
 */
function ZustandHydrationProvider({ children }: { children: React.ReactNode }) {
  // No need to fetchInventory here; hydration is automatic
  return <>{children}</>;
}

/**
 * Root layout component for the Next.js application.
 * @param {Object} props - The component props.
 * @param {React.ReactNode} props.children - The child components to be rendered within the layout.
 * @returns {JSX.Element} The root HTML structure with the application content.
 */
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <ToastProvider>
          <ZustandHydrationProvider>{children}</ZustandHydrationProvider>
        </ToastProvider>
      </body>
    </html>
  );
}