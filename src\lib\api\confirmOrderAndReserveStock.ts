import { supabase } from '@/lib/supabaseClient';
import { publishInventoryUpdate } from '@/lib/realtime/publishInventoryUpdate';

/**
 * Confirms an order: sets status to 'active', updates order fields, and reserves product quantities.
 * @param orderUpdate - Object with order_id and fields to update in the orders table.
 * @param billItems - Array of order bill items with product_id and quantity.
 * @returns Promise<boolean> - True if successful, false otherwise.
 */
export async function confirmOrderAndReserveStock(
  orderUpdate: {
    order_id: string;
    order_dispatched_by: string;
    dispatched_by_display_name: string | null;
    delivered_by: string | undefined;
    delivered_by_display_name: string | undefined;
    dispatched_at: string;
  },
  billItems: { product_id: string; quantity: number }[]
): Promise<boolean> {
  // 1. Update order fields and status
  const { error: orderError } = await supabase
    .from('orders')
    .update({
      order_status: 'active',
      order_dispatched_by: orderUpdate.order_dispatched_by,
      dispatched_by_display_name: orderUpdate.dispatched_by_display_name,
      order_delivered_by: orderUpdate.delivered_by,
      delivered_by_display_name: orderUpdate.delivered_by_display_name,
      dispatched_at: orderUpdate.dispatched_at,
    })
    .eq('order_id', orderUpdate.order_id);
  if (orderError) return false;

  // 2. Fetch current product quantities
  const productIds = billItems.map(item => item.product_id);
  const { data: products, error } = await supabase
    .from('products')
    .select('product_id, quantity_reserved')
    .in('product_id', productIds);
  if (error || !products) return false;

  // 3. Prepare updates: increase quantity_reserved
  const updates = products.map(product => {
    const billItem = billItems.find(item => item.product_id === product.product_id);
    const reserved = (product.quantity_reserved || 0) + (billItem?.quantity || 0);
    return {
      product_id: product.product_id,
      quantity_reserved: reserved,
      updated_at: new Date().toISOString(),
    };
  });

  // Log the data before updating product's quantity_reserved
  console.log('Updating product quantity_reserved with:', billItems);

  // 4. Update products in DB
  for (const update of updates) {
    const { error: updateError } = await supabase
      .from('products')
      .update({
        quantity_reserved: update.quantity_reserved,
        updated_at: update.updated_at,
      })
      .eq('product_id', update.product_id);
    if (updateError) return false;
  }

  // 5. Update local Zustand inventory store immediately
  try {
    const { updateInventoryItem } = require('@/lib/store/inventoryStore').useInventoryStore.getState();
    updates.forEach(update => {
      updateInventoryItem(update.product_id, {
        quantity_reserved: update.quantity_reserved,
        updated_at: update.updated_at
      });
    });
  } catch (err) {
    console.warn('Could not update local inventory store:', err);
  }

  // 6. Publish Ably event for realtime inventory update
  publishInventoryUpdate(updates.map(u => u.product_id));

  return true;
}
