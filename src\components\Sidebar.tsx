'use client';
import React from 'react';
import { useAuthStore } from '@/lib/store/authStore';
import { Button } from '@/components/ui/button';
import { useRouter, usePathname } from 'next/navigation';

/**
 * @typedef {object} NavLink
 * @property {string} href - The URL to navigate to.
 * @property {string} label - The text to display for the link.
 * @property {string[]} roles - The roles that can see this link.
 */

const navLinks = [
  { href: '/dashboard/admin', label: 'Admin Dashboard', roles: ['admin'] },
  { href: '/dashboard/admin/staff', label: 'Staff', roles: ['admin'] },
  { href: '/dashboard/admin/retailers', label: 'Retailers', roles: ['admin'] },
  { href: '/dashboard/admin/inventory', label: 'Inventory', roles: ['admin'] },
  { href: '/dashboard/admin/finance/overview', label: 'Finance Overview', roles: ['admin'] },
  { href: '/dashboard/admin/finance/reports', label: 'Finance Reports', roles: ['admin'] },
  { href: '/dashboard/admin/settings', label: 'Settings', roles: ['admin'] },
  { href: '/dashboard/reception', label: 'Reception Dashboard', roles: ['reception'] },
  { href: '/dashboard/reception/orders', label: 'Orders', roles: ['reception'] },
  { href: '/dashboard/reception/inventory', label: 'Inventory', roles: ['reception'] },
  { href: '/dashboard/reception/settings', label: 'Settings', roles: ['reception'] },
  { href: '/dashboard/finance', label: 'Finance Dashboard', roles: ['finance'] },
  { href: '/dashboard/finance/transactions', label: 'Transactions', roles: ['finance'] },
  { href: '/dashboard/finance/reports', label: 'Reports', roles: ['finance'] },
  { href: '/dashboard/finance/settings', label: 'Settings', roles: ['finance'] },
];

/**
 * Sidebar component for dashboard navigation.
 * Renders navigation links dynamically based on the user's role.
 * Uses Shadcn UI's Button component for styling.
 */
const Sidebar: React.FC = () => {
  const { user, role, clearAuth, _hasHydrated } = useAuthStore();
  const userRole = role;
  const router = useRouter();
  const pathname = usePathname(); // Get current pathname

  const handleLogout = async () => {
    await clearAuth();
    router.push('/login');
  };

  if (!_hasHydrated || !user) {
    return null; // Or a loading spinner
  }

  const filteredNavLinks = navLinks.filter(link => {
    const hasRole = userRole ? link.roles.includes(userRole) : false;
    return hasRole;
  });

  return (
    <aside className="w-64 bg-gray-800 text-white p-4 flex flex-col h-screen">
      <h2 className="text-2xl font-bold mb-6">
        {userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'HisabKitab'}
      </h2>
      <nav className="space-y-2 flex-grow">
        {_hasHydrated && user &&
          filteredNavLinks.map((link) => { // Use filteredNavLinks here
            const isActive = pathname === link.href || (link.href === '/dashboard/admin' && pathname === '/');
            return (
              <Button
                key={link.href}
                variant={isActive ? "secondary" : "ghost"} // Apply 'secondary' variant if active
                className="w-full justify-start"
                onClick={() => router.push(link.href)}
              >
                {link.label}
              </Button>
            );
          })}
      </nav>
      {_hasHydrated && user && (
        <div className="mt-auto">
          <Button onClick={handleLogout} variant="destructive" className="w-full">
            Logout
          </Button>
        </div>
      )}
    </aside>
  );
};

export default Sidebar;