import type { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  const { retailer_id, retailer_name, retailer_location, retailer_number } = req.body;
  if (!retailer_id || !retailer_name || !retailer_location || !retailer_number) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  const { data, error } = await supabase
    .from('retailer')
    .update({ retailer_name, retailer_location, retailer_number })
    .eq('retailer_id', retailer_id)
    .select();
  if (error) return res.status(400).json({ error: error.message });
  if (!data || data.length === 0) {
    return res.status(400).json({ error: 'No retailer updated. Check retailer_id.' });
  }
  return res.status(200).json({ success: true, updated: data[0] });
}
