/**
 * @module @/lib/api/supabase
 * @description Supabase client initialization and data fetching utilities.
 */

import { createClient } from '@supabase/supabase-js';
import { Product } from '@/database/schema';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase URL or Anon Key environment variables.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Fetches all inventory data from Supabase in batches.
 * @param {number} batchSize - The number of rows to fetch per batch. Defaults to 1000.
 * @returns {Promise<InventoryItem[]>} A promise that resolves to an array of all InventoryItem.
 */
export async function fetchAllInventoryBatched(): Promise<Product[]> {
  const { data: products, error: productsError } = await supabase
    .from('products')
    .select('*');

  if (productsError) {
    console.error('Error fetching all products:', productsError);
    throw productsError;
  }

  return products || [];
}

/**
 * Fetches only product data from Supabase.
 * @returns {Promise<Product[]>} A promise that resolves to an array of Product.
 */
export async function fetchProductData(offset: number, limit: number, countOnly: boolean = false): Promise<Product[] | number> {
  if (countOnly) {
    const { count, error: productsError } = await supabase
      .from('products')
      .select('*', { count: 'exact' });

    if (productsError) {
      throw productsError;
    }
    return count as number;
  } else {
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .range(offset, offset + limit - 1);

    if (productsError) {
      throw productsError;
    }
    return products as Product[];
  }
}