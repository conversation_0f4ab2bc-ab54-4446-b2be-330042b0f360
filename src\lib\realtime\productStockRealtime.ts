import ably from './ablyClient';

/**
 * Publishes a product stock update event to the Ably channel.
 * @param productIds The product IDs that were updated.
 */
export function publishProductStockUpdate(productIds: string[]) {
  const channel = ably.channels.get('products');
  channel.publish('product-stock-updated', { productIds });
}

/**
 * Subscribes to product stock update events and calls the callback with the productIds.
 * @param callback Function to call when a product stock update event is received.
 * @returns Unsubscribe function.
 */
export function subscribeToProductStockUpdates(callback: (productIds: string[]) => void) {
  const channel = ably.channels.get('products');
  const handler = (msg: any) => {
    const { productIds } = msg.data;
    callback(productIds);
  };
  channel.subscribe('product-stock-updated', handler);
  return () => channel.unsubscribe('product-stock-updated', handler);
}
