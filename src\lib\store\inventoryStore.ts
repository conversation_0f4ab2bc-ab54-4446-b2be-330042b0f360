import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { InventoryItem, Product } from '@/database/schema';
import { fetchProductData } from '@/lib/api/supabase';


/**
 * @interface InventoryState
 * @property {InventoryItem[]} inventoryItems - The list of inventory items.
 * @property {boolean} isInitialLoading - Indicates if the inventory data is being loaded for the first time (no cached data).
 * @property {boolean} isRefreshing - Indicates if the inventory data is currently being refreshed from the backend.
 * @property {string | null} error - Stores any error message during data fetching.
 * @property {Date | null} lastUpdated - Timestamp of the last successful inventory data update.
 * @property {string} searchTerm - The current search term applied to inventory items.
 * @property {string | null} selectedCategory - The currently selected product category for filtering.
 * @property {string | null} selectedPowerType - The currently selected power type for filtering.
 * @property {boolean} showLensParameters - Flag to show/hide lens-specific filter parameters.
 * @property {InventoryItem[]} advancedFilteredItems - Inventory items after applying advanced filters.
 * @property {number | null} selectedSphericalValue - The selected spherical value for filtering.
 * @property {number | null} selectedCylindricalValue - The selected cylindrical value for filtering.
 * @property {number | null} selectedAddValue - The selected add value for filtering.
 * @property {number | null} selectedAxisValue - The selected axis value for filtering.
 * @property {'Left' | 'Right' | null} selectedLensSide - The selected lens side for filtering.
 * @property {string | null} selectedProductCode - The currently selected product code for filtering.
 * @property {number} currentPage - The current page number for pagination.
 * @property {InventoryItem[]} selectedProducts - The list of currently selected inventory items.
 */
interface InventoryState {
  inventoryItems: InventoryItem[];
  isInitialLoading: boolean;
  isRefreshing: boolean;
  loadingProgress: number; // New state for loading progress (0-100)
  error: string | null;
  lastUpdated: Date | null;
  searchTerm: string;
  selectedCategory: string | null;
  selectedPowerType: string | null;
  showLensParameters: boolean;
  advancedFilteredItems: InventoryItem[];
  selectedSphericalValue: number | null;
  selectedCylindricalValue: number | null;
  selectedAddValue: number | null;
  selectedAxisValue: number | null;
  selectedLensSide: 'Left' | 'Right' | null;
  selectedProductCode: string | null;
  currentPage: number;
  selectedProducts: InventoryItem[];
}

/**
 * @interface InventoryActions
 * @property {(items: InventoryItem[]) => void} setInventoryItems - Sets the inventory items and updates the last updated timestamp.
 * @property {(loading: boolean) => void} setIsInitialLoading - Sets the initial loading state.
 * @property {(refreshing: boolean) => void} setIsRefreshing - Sets the refreshing state.
 * @property {(error: string | null) => void} setError - Sets an error message, or clears it if null.
 * @property {(product_id: string, updates: Partial<InventoryItem>) => void} updateInventoryItem - Updates a specific inventory item by its product ID.
 * @property {(item: InventoryItem) => void} addInventoryItem - Adds a new inventory item to the store and IndexedDB.
 * @property {() => Promise<void>} fetchInventory - Asynchronously fetches all inventory data from Supabase and caches it.
 * @property {(term: string) => void} setSearchTerm - Sets the search term for filtering inventory items.
 * @property {(category: string | null) => void} setSelectedCategory - Sets the selected product category for filtering.
 * @property {(powerType: string | null) => void} setSelectedPowerType - Sets the selected power type for filtering.
 * @property {(value: number | null) => void} setSelectedSphericalValue - Sets the selected spherical value for filtering.
 * @property {(value: number | null) => void} setSelectedCylindricalValue - Sets the selected cylindrical value for filtering.
 * @property {(value: number | null) => void} setSelectedAddValue - Sets the selected add value for filtering.
 * @property {(value: number | null) => void} setSelectedAxisValue - Sets the selected axis value for filtering.
 * @property {(value: 'Left' | 'Right' | null) => void} setSelectedLensSide - Sets the selected lens side for filtering.
 * @property {(code: string | null) => void} setSelectedProductCode - Sets the selected product code for filtering.
 * @property {() => Promise<void>} applyFilters - Applies all selected advanced filters to the inventory items. **Consumers should reset pagination to page 1 after calling this.**
 * @property {() => void} clearFilters - Clears all selected advanced filters and resets the filtered items. **Consumers should reset pagination to page 1 after calling this.**
 * @property {() => InventoryItem[]} getFilteredInventoryItems - Returns the inventory items filtered by the current search term and advanced filters.
 * @property {() => void} toggleLensParameters - Toggles the visibility of lens-specific filter parameters.
 * @property {(page: number) => void} setCurrentPage - Sets the current page number for pagination.
 * @property {() => void} resetPagination - Resets the current page number to 1.
 * @property {(products: InventoryItem[]) => void} setSelectedProducts - Sets the list of selected inventory items.
 * @property {() => void} clearSelectedProducts - Clears all selected inventory products.
 */
interface InventoryActions {
  setInventoryItems: (items: InventoryItem[]) => void;
  setIsInitialLoading: (loading: boolean) => void;
  setIsRefreshing: (refreshing: boolean) => void;
  setLoadingProgress: (progress: number) => void; // New action for loading progress
  setError: (error: string | null) => void;
  updateInventoryItem: (product_id: string, updates: Partial<InventoryItem>) => void;
  addInventoryItem: (item: InventoryItem) => void;
  fetchInventory: (forceRefresh?: boolean) => Promise<void>;
  setSearchTerm: (term: string) => void;
  setSelectedCategory: (category: string | null) => void;
  setSelectedPowerType: (powerType: string | null) => void;
  setSelectedSphericalValue: (value: number | null) => void;
  setSelectedCylindricalValue: (value: number | null) => void;
  setSelectedAddValue: (value: number | null) => void;
  setSelectedAxisValue: (value: number | null) => void;
  setSelectedLensSide: (value: 'Left' | 'Right' | null) => void;
  setSelectedProductCode: (code: string | null) => void;
  applyFilters: () => void;
  clearFilters: () => void;
  getFilteredInventoryItems: () => InventoryItem[];
  toggleLensParameters: () => void;
  setCurrentPage: (page: number) => void;
  resetPagination: () => void;
  setSelectedProducts: (products: InventoryItem[]) => void;
  clearSelectedProducts: () => void;
}

/**
 * Zustand store for managing inventory state and actions.
 * This store handles fetching, filtering, and updating inventory items.
 * It also integrates with IndexedDB for caching and Supabase for real-time updates.
 * @returns {InventoryState & InventoryActions} The inventory store with its state and actions.
 */



import { db } from '@/lib/database/indexeddb';

/**
 * Zustand store for managing inventory state and actions.
 * This store handles fetching, filtering, and updating inventory items, including selected products.
 * It also integrates with IndexedDB for caching and Supabase for real-time updates.
 * @returns {InventoryState & InventoryActions} The inventory store with its state and actions.
 */
export const useInventoryStore = create<InventoryState & InventoryActions>()(
  persist(
    (set, get) => ({
      inventoryItems: [],
      isInitialLoading: true,
      isRefreshing: false,
      loadingProgress: 0, // Initialize loading progress
      error: null,
      lastUpdated: null,
      searchTerm: '',
      selectedCategory: null,
      selectedPowerType: null,
      showLensParameters: false,
      advancedFilteredItems: [],
      selectedSphericalValue: null,
      selectedCylindricalValue: null,
      selectedAddValue: null,
      selectedAxisValue: null,
      selectedLensSide: null,
      selectedProductCode: null,
      currentPage: 1, // Initialize current page to 1
      selectedProducts: [], // Initialize selected products
      setInventoryItems: (items) => {
        set({ inventoryItems: items, lastUpdated: new Date() });
        // Always reapply filters to ensure advancedFilteredItems is in sync
        get().applyFilters();
      },
      setIsInitialLoading: (loading) => set({ isInitialLoading: loading }),
      setIsRefreshing: (refreshing) => set({ isRefreshing: refreshing }),
      setLoadingProgress: (progress) => set({ loadingProgress: progress }), // Implement new action
      setError: (error) => set({ error }),
      toggleLensParameters: () => set((state) => ({ showLensParameters: !state.showLensParameters })),
      setSearchTerm: (term) => {
        set({ searchTerm: term });
        get().applyFilters(); // Re-apply filters when search term changes
      },
      setSelectedCategory: (category) => set({ selectedCategory: category }),
      setSelectedPowerType: (powerType) => set({ selectedPowerType: powerType }),
      setSelectedSphericalValue: (value) => set({ selectedSphericalValue: value }),
      setSelectedCylindricalValue: (value) => set({ selectedCylindricalValue: value }),
      setSelectedAddValue: (value) => set({ selectedAddValue: value }),
      setSelectedAxisValue: (value) => set({ selectedAxisValue: value }),
      setSelectedLensSide: (value) => set({ selectedLensSide: value }),
      setSelectedProductCode: (code) => set({ selectedProductCode: code }),
      setCurrentPage: (page) => set({ currentPage: page }),
      resetPagination: () => set({ currentPage: 1 }),
      /**
       * Sets the list of currently selected inventory items.
       * @param {InventoryItem[]} products - The array of selected inventory items.
       */
      setSelectedProducts: (products: InventoryItem[]) => set({ selectedProducts: products }),
      /**
       * Clears all selected inventory products.
       */
      clearSelectedProducts: () => set({ selectedProducts: [] }),
      clearFilters: () => {
        set({
          selectedCategory: null,
          selectedPowerType: null,
          selectedSphericalValue: null,
          selectedCylindricalValue: null,
          selectedAddValue: null,
          selectedAxisValue: null,
          selectedLensSide: null,
          selectedProductCode: null,
          advancedFilteredItems: get().inventoryItems, // Reset to all items
          currentPage: 1, // Reset pagination to page 1
        });
      },
      applyFilters: () => {
        const { selectedCategory, selectedPowerType, selectedSphericalValue, selectedCylindricalValue, selectedAddValue, selectedAxisValue, selectedLensSide, selectedProductCode, inventoryItems, searchTerm } = get();
        console.log('[Store] applyFilters called, inventoryItems length:', inventoryItems.length);

        let filtered = inventoryItems;

        // Filter by category
        if (selectedCategory) {
          filtered = filtered.filter(item => item.product_category?.toLowerCase().includes(selectedCategory.toLowerCase()));
        }

        // Filter by power type
        if (selectedPowerType) {
          filtered = filtered.filter(item => item.power_type?.toLowerCase().includes(selectedPowerType.toLowerCase()));
        }

        // Filter by spherical value
        if (selectedSphericalValue !== null) {
          filtered = filtered.filter(item => item.spherical_value === selectedSphericalValue);
        }

        // Filter by cylindrical value
        if (selectedCylindricalValue !== null) {
          filtered = filtered.filter(item => item.cylindrical_value === selectedCylindricalValue);
        }

        // Filter by add value
        if (selectedAddValue !== null) {
          filtered = filtered.filter(item => item.add_value === selectedAddValue);
        }

        // Filter by axis value
        if (selectedAxisValue !== null) {
          filtered = filtered.filter(item => item.axis_value === selectedAxisValue);
        }

        // Filter by lens side
        if (selectedLensSide !== null) {
          filtered = filtered.filter(item => item.lens_side?.toLowerCase() === selectedLensSide.toLowerCase());
        }

        // Filter by product code
        if (selectedProductCode) {
          filtered = filtered.filter(item => item.product_code?.toString().toLowerCase().includes(selectedProductCode.toLowerCase()));
        }

        // Apply search term to the already advanced filtered items
        if (searchTerm) {
          filtered = filtered.filter(item => item.product_code?.toString().toLowerCase().includes(searchTerm.toLowerCase()));
        }

        set({ advancedFilteredItems: filtered, currentPage: 1 });
      },
      getFilteredInventoryItems: () => {
        const { advancedFilteredItems } = get();
        // Sort by entry_date in descending order (newest first)
        return advancedFilteredItems.sort((a, b) => {
          const dateA = a.entry_date ? new Date(a.entry_date).getTime() : 0;
          const dateB = b.entry_date ? new Date(b.entry_date).getTime() : 0;
          return dateB - dateA;
        });
      },
      updateInventoryItem: async (product_id, updates) => {
        console.log('[Store] updateInventoryItem called for:', product_id, updates);
        set((state) => ({
          inventoryItems: state.inventoryItems.map((item) =>
            item.product_id === product_id ? { ...item, ...updates } : item
          ),
          lastUpdated: new Date(),
        }));
        // Update in IndexedDB
        const updatedItem = get().inventoryItems.find(item => item.product_id === product_id);
        if (updatedItem) {
          await db.inventoryItems.put({ ...updatedItem, ...updates });
        }
        // Reapply filters to update advancedFilteredItems with the new data
        console.log('[Store] applying filters after update');
        get().applyFilters();
        console.log('[Store] filters applied, advancedFilteredItems length:', get().advancedFilteredItems.length);
      },
      addInventoryItem: async (item) => {
        set((state) => ({
          inventoryItems: [...state.inventoryItems, item],
        }));
        await db.inventoryItems.put(item);
        // Reapply filters to update advancedFilteredItems with the new data
        get().applyFilters();
      },
      updateProductsQuantity: async (productUpdates: Array<{ product_id: string; quantity_total: number; quantity_added: number; quantity_left: number }>) => {
        set((state) => ({
          inventoryItems: state.inventoryItems.map((item) => {
            const update = productUpdates.find((pu) => pu.product_id === item.product_id);
            if (update) {
              return {
                ...item,
                quantity_total: update.quantity_total,
                quantity_added: update.quantity_added,
                quantity_left: update.quantity_left,
                updated_at: new Date().toISOString()
              };
            }
            return item;
          }),
        }));
        // Update all in IndexedDB
        for (const update of productUpdates) {
          const updatedItem = get().inventoryItems.find(item => item.product_id === update.product_id);
          if (updatedItem) {
            await db.inventoryItems.put({ ...updatedItem, ...update });
          }
        }
        // Reapply filters to update advancedFilteredItems with the new data
        get().applyFilters();
      },
      fetchInventory: async (forceRefresh = false) => {
        const { setIsInitialLoading, setIsRefreshing, setError, setInventoryItems, setLoadingProgress, inventoryItems } = get();

        try {
          if (inventoryItems.length === 0) {
            setIsInitialLoading(true);
          } else {
             setIsRefreshing(true);
           }
           setError(null);
           setLoadingProgress(0);

           // Clear existing data if forcing refresh
           if (forceRefresh) {
             await db.inventoryItems.clear();
             setInventoryItems([]);
           }

           // First try to load from IndexedDB if not forcing refresh and no items in store
           if (!forceRefresh && inventoryItems.length === 0) {
             const cachedItems = await db.inventoryItems.toArray();
             if (cachedItems.length > 0) {
               setInventoryItems(cachedItems);
               setLoadingProgress(50);
               setIsInitialLoading(false); // Initial load from cache complete
             }
           }

          // Fetch from Supabase in batches
          const batchSize = 1000;
          let allProducts: InventoryItem[] = [];
          let offset = 0;
          let hasMore = true;
          
          while (hasMore) {
            const batch = await fetchProductData(offset, batchSize) as Product[];
            allProducts = [...allProducts, ...batch];
            offset += batchSize;
            hasMore = batch.length === batchSize;
            
            // Update progress (0-50% for IndexedDB, 50-100% for Supabase)
            const progress = 50 + (offset / (offset + batchSize)) * 50;
            setLoadingProgress(Math.min(99, Math.floor(progress)));
          }

          setInventoryItems(allProducts);
          setLoadingProgress(100);

          // Persist to IndexedDB
          const batchPromises = [];
          for (let i = 0; i < allProducts.length; i += 100) {
            const batch = allProducts.slice(i, i + 100);
            batchPromises.push(db.inventoryItems.bulkPut(batch));
          }
          await Promise.all(batchPromises);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to fetch inventory');
        } finally {
          setIsInitialLoading(false);
          setIsRefreshing(false);
          setLoadingProgress(100);
        }
      },
    }),
    {
      name: 'inventory-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        inventoryItems: state.inventoryItems,
        lastUpdated: state.lastUpdated,
      }),
    }
  )
);